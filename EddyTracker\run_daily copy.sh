#!/bin/bash
# -*- coding: utf-8 -*-
#
# 海洋涡旋识别系统 - 每日自动化脚本
# ================================
#
# 该脚本用于每日自动执行涡旋识别流程
# 适用于cron定时任务或手动执行
#
# 作者：中星海洋
# 日期：2025-07-10

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="${SCRIPT_DIR}"
PYTHON_CMD="python3"
MAIN_SCRIPT="${PROJECT_ROOT}/main.py"
CONFIG_FILE="${PROJECT_ROOT}/config/eddy.yaml"
LOG_DIR="${PROJECT_ROOT}/log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查运行环境和依赖..."
    
    # 检查Python
    if ! command -v ${PYTHON_CMD} &> /dev/null; then
        log_error "Python3 未安装或不在PATH中"
        exit 1
    fi
    
    # 检查主程序文件
    if [[ ! -f "${MAIN_SCRIPT}" ]]; then
        log_error "主程序文件不存在: ${MAIN_SCRIPT}"
        exit 1
    fi
    
    # 检查配置文件
    if [[ ! -f "${CONFIG_FILE}" ]]; then
        log_error "配置文件不存在: ${CONFIG_FILE}"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p "${LOG_DIR}"
    
    log_info "环境检查通过"
}

# 运行涡旋识别
run_eddy_identification() {
    local target_date="$1"
    local extra_args="$2"
    
    log_info "开始执行涡旋识别流程..."
    log_info "目标日期: ${target_date:-今天}"
    log_info "工作目录: ${PROJECT_ROOT}"
    
    # 构建命令
    local cmd="${PYTHON_CMD} ${MAIN_SCRIPT}"
    
    if [[ -n "${target_date}" ]]; then
        cmd="${cmd} --date ${target_date}"
    fi
    
    if [[ -n "${extra_args}" ]]; then
        cmd="${cmd} ${extra_args}"
    fi
    
    # 执行命令
    log_info "执行命令: ${cmd}"
    cd "${PROJECT_ROOT}"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 执行并捕获输出
    if eval "${cmd}"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_info "涡旋识别完成，耗时: ${duration}秒"
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_error "涡旋识别失败，耗时: ${duration}秒"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
海洋涡旋识别系统 - 每日自动化脚本

用法: $0 [选项]

选项:
  -d, --date DATE       指定处理日期 (格式: YYYY-MM-DD)
  -y, --yesterday       处理昨天的数据
  -v, --verbose         显示详细输出
  -h, --help           显示此帮助信息

示例:
  $0                    # 处理今天的数据
  $0 -d 2025-07-10      # 处理指定日期
  $0 -y                 # 处理昨天的数据

  
EOF
}

# 主函数
main() {
    local target_date=""
    local cleanup_days=""
    local verbose_mode="false"
    local yesterday_mode="false"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--date)
                target_date="$2"
                shift 2
                ;;
            -y|--yesterday)
                yesterday_mode="true"
                shift
                ;;
            -v|--verbose)
                verbose_mode="true"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 处理昨天模式
    if [[ "${yesterday_mode}" == "true" ]]; then
        target_date=$(date -d "yesterday" +%Y-%m-%d)
        log_info "昨天模式：设置目标日期为 ${target_date}"
    fi
    
    # 显示脚本信息
    log_info "海洋涡旋识别系统 - 每日自动化脚本启动"
    log_info "脚本路径: $0"
    log_info "工作目录: ${PROJECT_ROOT}"
    
    # 检查环境
    check_dependencies
    
    # 构建额外参数
    local extra_args=""
    if [[ "${verbose_mode}" == "true" ]]; then
        extra_args="${extra_args} --verbose"
    fi
    
    # 运行涡旋识别
    if run_eddy_identification "${target_date}" "${extra_args}"; then
        log_info "每日自动化流程执行成功"
        send_notification "success" "日期: ${target_date:-今天}"
        exit 0
    else
        log_error "每日自动化流程执行失败"
        send_notification "failure" "日期: ${target_date:-今天}"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 