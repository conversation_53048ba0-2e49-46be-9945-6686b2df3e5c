# 用户数据处理功能说明

## 功能概述

用户数据处理功能可以根据用户指定的经纬度范围，自动将全球涡旋识别结果裁切为用户特定的区域数据，并生成独立的GeoJSON文件。

## 配置说明

### 1. 配置文件设置

在 `config/eddy.yaml` 中添加以下配置：

```yaml
# 用户数据处理配置
user_data:
  # 用户参数文件目录
  user_params_dir: ./user_paras
  # 用户数据输出根目录
  base_dir: /data2/eddy/all_users
  # 是否启用用户数据处理
  enable_user_processing: true
  # 用户数据处理的geojson文件路径模板
  output_filename: eddies.geojson
  # 是否在处理完成后更新用户参数文件的processed状态
  update_processed_status: true
```

### 2. 用户参数文件格式

用户参数文件应放置在 `./user_paras/` 目录下，文件名格式为 `用户名_日期_时间.json`。

文件内容示例：
```json
{
  "userName": "jiangwei001",
  "area": "[[147.10047,0],[178.61644,-41]]",
  "timestamp": 1752558479178,
  "datetime": "2025-07-15 13:47:59",
  "processed": false
}
```

#### 字段说明：
- `userName`: 用户名，用于创建用户特定的输出目录
- `area`: 经纬度范围，格式为 `[[经度1,纬度1],[经度2,纬度2]]`
- `timestamp`: 时间戳
- `datetime`: 人类可读的日期时间
- `processed`: 是否已处理标记（系统会自动更新）

## 输出目录结构

```
/data2/eddy/all_users/
├── jiangwei001/                   # 用户目录
│   └── 2025_07_16/               # 日期目录
│       └── eddies.geojson        # 用户特定的涡旋数据
├── fzq06251/
│   └── 2025_07_16/
│       └── eddies.geojson
└── ...
```

## 使用方法

### 1. 自动处理（推荐）

用户数据处理功能已集成到主程序中，只需正常运行涡旋识别即可：

```bash
# 激活conda环境
conda activate eddy

# 运行主程序，会自动处理用户数据
python main.py

# 或使用shell脚本
./run_daily.sh
```

### 2. 手动测试

可以使用测试脚本验证功能：

```bash
python test_user_processor.py
```

## 功能特点

### 1. 自动裁切
- 基于用户指定的经纬度范围自动裁切全球涡旋数据
- 支持多种几何类型：Point、Polygon、MultiPolygon
- 智能边界检测，确保涡旋与用户区域有交集

### 2. 批量处理
- 自动读取所有用户参数文件
- 并行处理多个用户的数据需求
- 详细的处理日志和错误报告

### 3. 状态跟踪
- 自动更新用户参数文件的处理状态
- 记录处理日期和涡旋数量
- 避免重复处理同一用户的数据

### 4. 错误处理
- 健壮的错误处理机制
- 单个用户处理失败不影响其他用户
- 详细的错误日志记录

## 处理流程

1. **配置检查**: 检查是否启用用户数据处理功能
2. **参数读取**: 从 `./user_paras/` 目录读取所有用户参数文件
3. **数据裁切**: 根据每个用户的经纬度范围裁切全球GeoJSON数据
4. **结果保存**: 为每个用户创建独立的输出目录和文件
5. **状态更新**: 更新用户参数文件的处理状态
6. **日志记录**: 记录详细的处理过程和结果

## 日志示例

```
2025-07-16 14:30:00 - UserDataProcessor - INFO - 找到 3 个用户参数文件
2025-07-16 14:30:01 - UserDataProcessor - INFO - 处理用户: jiangwei001
2025-07-16 14:30:01 - UserDataProcessor - INFO - 用户 jiangwei001 的区域边界: (147.10047, -41.0, 178.61644, 0.0)
2025-07-16 14:30:02 - UserDataProcessor - INFO - 用户 jiangwei001 处理完成: 15 个涡旋，保存到 /data2/eddy/all_users/jiangwei001/2025_07_16/eddies.geojson
2025-07-16 14:30:02 - UserDataProcessor - INFO - 用户数据处理完成: 成功 3 个，失败 0 个
```

## 注意事项

1. **区域格式**: 确保area字段中的经纬度坐标格式正确
2. **目录权限**: 确保有权限在 `/data2/eddy/all_users/` 目录下创建文件
3. **依赖库**: 需要安装 `shapely>=1.8.0` 库
4. **数据完整性**: 用户数据处理依赖于全球涡旋识别结果的完整性

## 故障排除

### 1. 找不到用户参数文件
- 检查 `./user_paras/` 目录是否存在
- 确认文件格式为 `.json`
- 检查文件内容是否为有效的JSON格式

### 2. 区域解析失败
- 检查area字段的格式是否正确
- 确保经纬度坐标在有效范围内
- 验证JSON字符串格式

### 3. 输出目录创建失败
- 检查 `/data2/eddy/all_users/` 目录的写入权限
- 确认磁盘空间充足
- 检查用户名是否包含特殊字符

### 4. 处理结果为空
- 检查用户指定的区域是否与全球涡旋数据有交集
- 确认全球GeoJSON文件是否存在且有效
- 检查涡旋数据的坐标范围 