# 海洋涡旋自动识别系统 (Automated Eddy Detection System)

基于py_eddy_tracker库的全自动化海洋涡旋识别与数据处理系统

##  系统功能

1. **自动化流程**: 
   - 自动更新配置文件日期
   - 自动下载Copernicus Marine Service ADT数据
   - 自动执行涡旋识别算法
   - 自动生成NetCDF和GeoJSON格式输出

2. **数据处理**: 
   - 海表高度(ADT)数据预处理
   - 贝塞尔高通滤波去除大尺度信号
   - 基于等值线的涡旋识别算法
   - 气旋和反气旋涡旋分类

3. **输出格式**: 
   - NetCDF格式
   - GeoJSON格式
   - 完整的日志记录

4. **用户数据处理**:
   - 根据用户指定经纬度范围自动裁切全球涡旋数据
   - 批量处理多个用户的数据需求
   - 自动生成用户特定的GeoJSON文件
   - 支持用户参数文件管理

5. **部署运维**:
   - 命令行工具支持
   - 定时任务自动化
   - 错误处理和重试机制
   - 日志轮转和清理

##  Environment Dependencies

### Option 1: Using Conda Environment (Recommended)
```bash
# Create conda environment
conda create -n eddy python=3.12
conda activate eddy

# Install dependencies
conda install -c conda-forge py-eddy-tracker netcdf4 numpy pyyaml
pip install copernicusmarine
```

### Option 2: Using pip
```bash
# Install Python dependencies
pip install py-eddy-tracker copernicusmarine netCDF4 numpy pyyaml

# Or use requirements.txt
pip install -r requirements.txt
```

##  项目结构

```
EddyTracker/
├── main.py                    # 主程序入口
├── run_daily.sh              # 每日自动化脚本
├── requirements.txt          # Python依赖包列表
├── test_user_processor.py   # 用户数据处理测试脚本
├── create_user_params.py    # 用户参数文件创建工具
├── config/
│   ├── eddy.yaml            # 主配置文件
│   └── update/
│       └── update.yaml      # 日期更新配置
├── utils/                   # 核心工具模块
│   ├── __init__.py         # 模块初始化文件
│   ├── eddy_processor.py   # 涡旋处理器核心类
│   ├── adt_download.py     # ADT数据下载器
│   ├── update_dates.py     # 配置文件日期更新工具
│   └── user_data_processor.py # 用户数据处理模块
├── user_paras/              # 用户参数文件目录
│   ├── jiangwei001_20250715_134759.json
│   ├── fzq06251_20250715_092716.json
│   └── ...
├── log/                     # 日志文件目录
├── README.md               # 项目说明文档
└── USER_DATA_PROCESSING.md # 用户数据处理功能说明
```

##  快速开始

### 1. 配置认证信息

编辑 `config/eddy.yaml` 文件，设置Copernicus Marine Service认证信息：

```yaml
credentials:
  copernicus_username: 'your_username'
  copernicus_password: 'your_password'
```

### 2. Command Line Usage

```bash
# Activate conda environment first
conda activate eddy

# Process today's data
python main.py

# Process specific date data
python main.py --date 2025-07-10

# Process yesterday's data
python main.py --yesterday

# Show verbose output
python main.py --verbose
```

### 3. Automation Script

```bash
# Using shell script (requires conda environment 'eddy')
./run_daily.sh                    # Process today's data
```

### 4. User Data Processing

系统支持根据用户指定的经纬度范围自动裁切全球涡旋数据：


用户参数文件格式 (放置在 `user_paras/` 目录下):
```json
{
  "userName": "jiangwei001",
  "area": "[[147.10047,0],[178.61644,-41]]",
  "timestamp": 1752558479178,
  "datetime": "2025-07-15 13:47:59",
  "processed": false
}
```

处理完成后，用户特定的数据将保存到：
```
/data2/eddy/all_users/jiangwei001/2025_07_16/eddies.geojson
```

详细说明请参考：[用户数据处理功能说明](USER_DATA_PROCESSING.md)

### 5. Scheduled Tasks Setup

#### Option 1: Using tmux session (Recommended)
```bash
# Create tmux session
tmux new-session -d -s eddy

# In tmux session, run daily automation
tmux send-keys -t eddy "cd /path/to/EddyTracker" C-m
tmux send-keys -t eddy "conda activate eddy" C-m
tmux send-keys -t eddy "./run_daily.sh -y" C-m
```

##  输出格式说明

### NetCDF文件 (.nc)
- `Anticyclonic_YYYYMMDD.nc` - 反气旋涡数据
- `Cyclonic_YYYYMMDD.nc` - 气旋涡数据
- 包含完整的涡旋属性和轮廓坐标信息

### GeoJSON文件 (.geojson)
标准的地理空间数据格式，结构如下：

```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": {
        "type": "Polygon",
        "coordinates": [[[lon1, lat1], [lon2, lat2], ...]]
      },
      "properties": {
        "eddy_type": "anticyclonic",
        "eddy_id": "anticyclonic_0",
        "longitude": 25.5,
        "latitude": 35.2,
        "amplitude": 0.15,
        "speed_radius": 45000,
        "effective_radius": 52000,
        "speed_average": 0.32
      }
    }
  ]
}
```

## ️ 参数配置

### 核心参数说明

| 参数 | 描述 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `filter_wavelength` | 滤波波长(km) | 500 | 300-700 |
| `step` | 等值线步长(m) | 0.002 | 0.001-0.005 |
| `pixel_limit` | 像素数量限制 | (5, 2000) | 根据分辨率调整 |
| `shape_error` | 形状误差阈值(%) | 55.0 | 40-70 |


##  自动化工作流程

系统按以下步骤自动执行：

1. **配置更新** ️
   - 自动更新 `config/eddy.yaml` 中的日期信息
   - 更新输入输出文件路径
   - 更新日志文件路径

2. **数据下载** ️
   - 从Copernicus Marine Service下载ADT数据
   - 数据保存到 `/data2/eddy/YYYY_MM_DD/input/` 目录
   - 支持断点续传和重复下载检查

3. **数据预处理** 
   - 加载NetCDF格式的ADT数据
   - 应用贝塞尔高通滤波器（默认500km截止波长）
   - 去除大尺度海洋环流信号

4. **涡旋识别** 
   - 基于等值线分析识别封闭轮廓
   - 几何形状验证（圆形拟合误差 < 55%）
   - 分类为气旋涡和反气旋涡

5. **数据导出** 
   - 生成NetCDF格式文件（科学研究用）
   - 转换为GeoJSON格式（前端渲染用）
   - 完整的元数据和属性信息

6. **用户数据处理** 
   - 读取用户参数文件
   - 根据用户指定的经纬度范围裁切全球涡旋数据
   - 为每个用户生成独立的GeoJSON文件
   - 更新用户参数文件的处理状态

7. **日志记录** 
   - 详细的处理过程日志
   - 错误信息和调试信息
   - 自动日志轮转和清理


##  配置文件详解

### 主配置文件 (`config/eddy.yaml`)

```yaml
# 认证信息
credentials:
  copernicus_username: 'your_username'  # Copernicus用户名
  copernicus_password: 'your_password'  # Copernicus密码

# 数据配置
data:
  date_folder: '2025_07_10'              # 数据日期（自动更新）
  base_dir: '/data2/eddy'                # 数据根目录
  longitude_range: [140, 290]            # 经度范围
  latitude_range: [-40, 20]              # 纬度范围

# 涡旋识别参数
eddy_identification:
  filter_wavelength: 500                 # 滤波波长(km)
  step: 0.002                           # 等值线步长(m)
  pixel_limit: [5, 2000]                # 像素数量限制
  shape_error: 55.0                     # 形状误差阈值(%)

# 输出配置
output:
  generate_netcdf: true                  # 生成NetCDF文件
  generate_geojson: true                 # 生成GeoJSON文件

# 日志配置
logging:
  level: 'INFO'                         # 日志级别
  max_file_size: 10                     # 日志文件最大大小(MB)
  backup_count: 5                       # 保留日志文件数量

# 用户数据处理配置
user_data:
  user_params_dir: './user_paras'       # 用户参数文件目录
  base_dir: '/data2/eddy/all_users'     # 用户数据输出根目录
  enable_user_processing: true          # 是否启用用户数据处理
  output_filename: 'eddies.geojson'     # 输出文件名
  update_processed_status: true         # 是否更新处理状态
```

##  致谢

- **[py-eddy-tracker](https://github.com/AntSimi/py-eddy-tracker)** - 核心涡旋识别算法
- **[Copernicus Marine Service](https://marine.copernicus.eu/)** - 提供海洋卫星数据
- **中星海洋** - 系统集成和自动化开发 