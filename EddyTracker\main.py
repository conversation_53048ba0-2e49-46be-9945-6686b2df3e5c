#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海洋涡旋自动识别系统 - 主程序
=============================

完整的自动化涡旋识别和数据处理流程：
1. 更新配置文件日期
2. 自动下载ADT数据
3. 涡旋识别和数据导出
4. 格式转换为GeoJSON
5. 完整的日志记录

作者：中星海洋
日期：2025-07-10
"""

import sys
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from utils.eddy_processor import EnhancedEddyProcessor
from utils.update_dates import update_all_dates, CONFIG as UPDATE_CONFIG
from utils.user_data_processor import UserDataProcessor


class EddyAutomationSystem:
    """涡旋自动化识别系统"""
    
    def __init__(self, config_file: str = "config/eddy.yaml"):
        """
        初始化自动化系统
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.processor = EnhancedEddyProcessor(config_file)
    
    def update_config_dates(self, target_date: datetime) -> bool:
        """
        更新配置文件日期
        
        Args:
            target_date: 目标日期
            
        Returns:
            bool: 是否更新成功
        """
        self.processor.logger.info("="*60)
        self.processor.logger.info("步骤1: 更新配置文件日期")
        self.processor.logger.info("="*60)
        
        try:
            # 更新主配置文件
            success = self.processor.update_config_date(target_date)
            
            if success:
                self.processor.logger.info(f" 配置文件日期更新成功: {target_date.strftime('%Y-%m-%d')}")
            else:
                self.processor.logger.error(" 配置文件日期更新失败")
                
            return success
            
        except Exception as e:
            self.processor.logger.error(f" 更新配置文件时发生错误: {e}")
            return False
    
    def download_data(self, target_date: datetime) -> str:
        """
        下载指定日期的数据
        
        Args:
            target_date: 目标日期
            
        Returns:
            str: 下载的文件路径
        """
        self.processor.logger.info("="*60)
        self.processor.logger.info("步骤2: 下载ADT数据")
        self.processor.logger.info("="*60)
        
        try:
            # 检查文件是否已存在
            expected_file = self.processor.config['data']['input_file']
            if Path(expected_file).exists():
                self.processor.logger.info(f" 数据文件已存在，跳过下载: {expected_file}")
                return expected_file
            
            # 下载数据
            file_path = self.processor.download_data(target_date)
            self.processor.logger.info(f" 数据下载成功: {file_path}")
            return file_path
            
        except Exception as e:
            self.processor.logger.error(f" 数据下载失败: {e}")
            raise
    
    def process_eddies(self, target_date: datetime, input_file: str) -> dict:
        """
        处理涡旋识别
        
        Args:
            target_date: 目标日期
            input_file: 输入文件路径
            
        Returns:
            dict: 处理结果
        """
        self.processor.logger.info("="*60)
        self.processor.logger.info("步骤3: 涡旋识别与数据处理")
        self.processor.logger.info("="*60)
        
        try:
            # 涡旋识别
            anticyclonic, cyclonic = self.processor.identify_eddies(input_file, target_date)
            
            # 导出NetCDF文件
            output_dir = self.processor.config['output']['output_dir']
            anticyclonic_file, cyclonic_file = self.processor.export_to_netcdf(
                anticyclonic, cyclonic, output_dir, target_date
            )
            
            # 转换为GeoJSON
            date_str = target_date.strftime("%Y%m%d")
            geojson_file = Path(output_dir) / f"eddies_{date_str}.geojson"
            self.processor.convert_to_geojson(
                anticyclonic_file, cyclonic_file, str(geojson_file)
            )
            
            results = {
                "anticyclonic_count": len(anticyclonic),
                "cyclonic_count": len(cyclonic),
                "total_eddies": len(anticyclonic) + len(cyclonic),
                "anticyclonic_nc": anticyclonic_file,
                "cyclonic_nc": cyclonic_file,
                "geojson": str(geojson_file),
                "output_dir": output_dir
            }
            
            self.processor.logger.info(f" 涡旋识别完成:")
            self.processor.logger.info(f"   - 反气旋涡: {results['anticyclonic_count']}个")
            self.processor.logger.info(f"   - 气旋涡: {results['cyclonic_count']}个")
            self.processor.logger.info(f"   - 总计: {results['total_eddies']}个涡旋")
            
            return results
            
        except Exception as e:
            self.processor.logger.error(f" 涡旋识别处理失败: {e}")
            raise
    
    def process_user_data(self, target_date: datetime, global_geojson_path: str) -> dict:
        """
        处理用户数据，根据用户参数裁切全球geojson文件
        
        Args:
            target_date: 目标日期
            global_geojson_path: 全球geojson文件路径
            
        Returns:
            dict: 用户数据处理结果
        """
        self.processor.logger.info("="*60)
        self.processor.logger.info("步骤4: 用户数据处理")
        self.processor.logger.info("="*60)
        
        try:
            # 检查是否启用用户数据处理
            if not self.processor.config.get('user_data', {}).get('enable_user_processing', False):
                self.processor.logger.info(" 用户数据处理功能未启用，跳过")
                return {
                    "enabled": False,
                    "processed_users": 0,
                    "message": "用户数据处理功能未启用"
                }
            
            # 创建用户数据处理器
            user_processor = UserDataProcessor(self.config_file)
            
            # 处理用户数据
            user_results = user_processor.process_user_data(global_geojson_path, target_date)
            
            if user_results['success']:
                self.processor.logger.info(f" 用户数据处理完成:")
                self.processor.logger.info(f"   - 成功处理: {user_results['processed_users']}个用户")
                self.processor.logger.info(f"   - 失败处理: {user_results['failed_users']}个用户")
                
                # 输出详细信息
                for user_detail in user_results['user_details']:
                    if user_detail['status'] == 'success':
                        self.processor.logger.info(f"   - 用户 {user_detail['username']}: {user_detail['filtered_count']}个涡旋")
                    else:
                        self.processor.logger.error(f"   - 用户 {user_detail['username']}: 处理失败 - {user_detail['error']}")
            else:
                self.processor.logger.error(f" 用户数据处理失败: {user_results.get('message', '未知错误')}")
            
            return {
                "enabled": True,
                **user_results
            }
            
        except Exception as e:
            self.processor.logger.error(f" 用户数据处理发生错误: {e}")
            return {
                "enabled": True,
                "success": False,
                "error": str(e)
            }
    
    def run_daily_automation(self, target_date: datetime = None) -> dict:
        """
        运行每日自动化流程
        
        Args:
            target_date: 目标日期，默认为今天
            
        Returns:
            dict: 完整的处理结果
        """
        if target_date is None:
            target_date = datetime.now()
        
        start_time = datetime.now()
        
        self.processor.logger.info(" 海洋涡旋自动识别系统启动")
        self.processor.logger.info(f" 目标日期: {target_date.strftime('%Y-%m-%d')}")
        self.processor.logger.info(f" 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.processor.logger.info("="*80)
        
        try:
            # 步骤1: 更新配置文件日期
            if not self.update_config_dates(target_date):
                raise RuntimeError("配置文件日期更新失败")
            
            # 步骤2: 下载数据
            input_file = self.download_data(target_date)
            
            # 步骤3: 涡旋识别和处理
            eddy_results = self.process_eddies(target_date, input_file)
            
            # 步骤4: 用户数据处理
            user_results = self.process_user_data(target_date, eddy_results['geojson'])
            
            # 计算处理时间
            end_time = datetime.now()
            processing_time = end_time - start_time
            
            # 汇总结果
            final_results = {
                "success": True,
                "target_date": target_date.strftime('%Y-%m-%d'),
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "processing_time_seconds": processing_time.total_seconds(),
                "processing_time_formatted": str(processing_time).split('.')[0],
                "input_file": input_file,
                "user_data_results": user_results,
                **eddy_results
            }
            
            # 成功完成日志
            self.processor.logger.info("="*80)
            self.processor.logger.info(" 自动化流程执行完成!")
            self.processor.logger.info(f" 处理结果概览:")
            self.processor.logger.info(f"   - 处理日期: {final_results['target_date']}")
            self.processor.logger.info(f"   - 处理时间: {final_results['processing_time_formatted']}")
            self.processor.logger.info(f"   - 涡旋总数: {final_results['total_eddies']}个")
            self.processor.logger.info(f"   - 输出目录: {final_results['output_dir']}")
            
            # 用户数据处理结果
            user_data_results = final_results['user_data_results']
            if user_data_results['enabled']:
                if user_data_results['success']:
                    self.processor.logger.info(f"   - 用户数据处理: 成功处理 {user_data_results['processed_users']} 个用户")
                else:
                    self.processor.logger.info(f"   - 用户数据处理: 失败 ({user_data_results.get('error', '未知错误')})")
            else:
                self.processor.logger.info(f"   - 用户数据处理: 未启用")
            
            self.processor.logger.info("="*80)
            
            return final_results
            
        except Exception as e:
            end_time = datetime.now()
            processing_time = end_time - start_time
            
            error_results = {
                "success": False,
                "target_date": target_date.strftime('%Y-%m-%d'),
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "processing_time_seconds": processing_time.total_seconds(),
                "error": str(e)
            }
            
            self.processor.logger.error("="*80)
            self.processor.logger.error(" 自动化流程执行失败!")
            self.processor.logger.error(f"错误信息: {e}")
            self.processor.logger.error(f"处理时间: {str(processing_time).split('.')[0]}")
            self.processor.logger.error("="*80)
            
            return error_results


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="海洋涡旋自动识别系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                          # 处理今天的数据
  python main.py --date 2025-07-10        # 处理指定日期
  python main.py --yesterday               # 处理昨天的数据
  python main.py --config custom.yaml     # 使用自定义配置文件
        """
    )
    
    parser.add_argument(
        '--date', '-d',
        type=str,
        help='指定处理日期 (格式: YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--yesterday', '-y',
        action='store_true',
        help='处理昨天的数据'
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/eddy.yaml',
        help='配置文件路径 (默认: config/eddy.yaml)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细输出'
    )
    
    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 确定目标日期
    if args.date:
        try:
            target_date = datetime.strptime(args.date, '%Y-%m-%d')
        except ValueError:
            print(" 日期格式错误，请使用 YYYY-MM-DD 格式")
            sys.exit(1)
    elif args.yesterday:
        target_date = datetime.now() - timedelta(days=1)
    else:
        target_date = datetime.now()
    
    # 检查配置文件是否存在
    if not Path(args.config).exists():
        print(f" 配置文件不存在: {args.config}")
        sys.exit(1)
    
    try:
        # 创建自动化系统
        automation = EddyAutomationSystem(args.config)
        
        # 运行自动化流程
        results = automation.run_daily_automation(target_date)
        
        # 输出结果摘要
        if not args.verbose:
            print("\n" + "="*60)
            if results['success']:
                print(f" 处理成功完成!")
                print(f" 处理日期: {results['target_date']}")
                print(f" 处理时间: {results['processing_time_formatted']}")
                print(f" 识别涡旋: {results['total_eddies']}个 (反气旋: {results['anticyclonic_count']}, 气旋: {results['cyclonic_count']})")
                print(f" 输出目录: {results['output_dir']}")
                if 'geojson' in results:
                    print(f" GeoJSON: {results['geojson']}")
                
                # 用户数据处理结果
                user_data_results = results.get('user_data_results', {})
                if user_data_results.get('enabled', False):
                    if user_data_results.get('success', False):
                        print(f" 用户数据处理: 成功处理 {user_data_results.get('processed_users', 0)} 个用户")
                    else:
                        print(f" 用户数据处理: 失败 ({user_data_results.get('error', '未知错误')})")
                else:
                    print(f" 用户数据处理: 未启用")
            else:
                print(f" 处理失败!")
                print(f"错误信息: {results['error']}")
            print("="*60)
        
        # 设置退出码
        sys.exit(0 if results['success'] else 1)
        
    except KeyboardInterrupt:
        print("\n️  用户中断程序执行")
        sys.exit(130)
    except Exception as e:
        print(f"\n 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 