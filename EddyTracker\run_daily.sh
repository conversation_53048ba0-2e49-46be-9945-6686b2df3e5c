#!/usr/bin/env bash
# 在tmux中运行EddyTracker项目，每天11:00执行(协商时间)

# 基本设置
export TZ='Asia/Shanghai'
SESSION_NAME="eddy"
SCRIPT_DIR="/workspaces/work/fisherycube-data-process/project_root/downloader/EddyTracker"
CONDA_ENV="eddy"

# 检查tmux会话
tmux has-session -t $SESSION_NAME 2>/dev/null
if [ $? != 0 ]; then
    # 创建新会话并执行任务
    tmux new-session -d -s $SESSION_NAME
else
    # 会话存在，清除现有内容
    tmux send-keys -t $SESSION_NAME C-c
    tmux send-keys -t $SESSION_NAME "clear" C-m
fi

# 执行任务
tmux send-keys -t $SESSION_NAME "cd $SCRIPT_DIR && while true; do
    source ~/anaconda3/etc/profile.d/conda.sh
    conda activate "$CONDA_ENV"
    clashoff # 开启代理
    now=\$(date +%s)
    next=\$([ \$(date +%s) -lt \$(date -d '11:00' +%s) ] && date -d '11:00' +%s || date -d 'tomorrow 11:00' +%s)
    echo [\$(date '+%F %T')] 等待下次执行...
    sleep \$((next - now))
    echo [\$(date '+%F %T')] 开始运行EddyTracker项目
    
    # 执行Python脚本
    timeout 0.5h python main.py > /dev/null 2>&1
    python_exit_code=\$?
    clashoff # 关闭代理
    
    echo [\$(date '+%F %T')] Python脚本执行完成（退出码：\$python_exit_code）
done" C-m


echo "已在tmux会话'$SESSION_NAME'中启动任务"
echo "使用'tmux attach -t $SESSION_NAME'查看状态" 