#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版海洋涡旋处理器
==================

本程序实现以下功能：
1. 从YAML配置文件加载参数
2. 自动下载ADT数据
3. 涡旋识别和数据导出
4. 完整的日志系统
5. 错误处理和重试机制

作者：中星海洋
日期：2025-07-08
"""

import os
import json
import logging
import yaml
from datetime import datetime
from pathlib import Path
from typing import Tuple, Dict, Any, Optional
from logging.handlers import RotatingFileHandler

import numpy as np
from netCDF4 import Dataset

from py_eddy_tracker.dataset.grid import RegularGridDataset
from py_eddy_tracker.observations.observation import EddiesObservations
from utils.adt_download import ADTDownloader


class EnhancedEddyProcessor:
    """增强版涡旋处理器类"""
    
    def __init__(self, config_file: str = "config/eddy.yaml"):
        """
        初始化增强版涡旋处理器
        
        Args:
            config_file: YAML配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
        self.logger = self._setup_logger()
        
        # 初始化ADT下载器
        self.downloader = None
        if self.config.get('credentials'):
            username = self.config['credentials'].get('copernicus_username')
            password = self.config['credentials'].get('copernicus_password')
            if username and password:
                self.downloader = ADTDownloader(username, password, self.logger)
    
    def _load_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            # 如果配置文件加载失败，使用默认配置
            print(f"警告：配置文件加载失败，使用默认配置: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'data': {
                'date_folder': datetime.now().strftime('%Y_%m_%d'),
                'base_dir': '/data2/eddy',
                'variables': ['adt', 'ugos', 'vgos'],
                'longitude_range': [140, 290],
                'latitude_range': [-40, 20],
                'variable_names': {
                    'longitude': 'longitude',
                    'latitude': 'latitude',
                    'adt': 'adt',
                    'u_velocity': 'ugos',
                    'v_velocity': 'vgos'
                }
            },
            'eddy_identification': {
                'filter_wavelength': 500,
                'filter_order': 3,
                'step': 0.002,
                'pixel_limit': [5, 2000],
                'shape_error': 55.0
            },
            'output': {
                'generate_netcdf': True,
                'generate_geojson': True
            },
            'logging': {
                'level': 'INFO',
                'log_dir': './log',
                'max_file_size': 10,
                'backup_count': 5
            },
            'automation': {
                'auto_download': True,
                'max_retry': 3
            }
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置增强版日志器"""
        logger = logging.getLogger(__name__)
        
        # 清除现有handlers
        logger.handlers.clear()
        
        # 设置日志级别
        log_level = self.config.get('logging', {}).get('level', 'INFO')
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # 日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        log_config = self.config.get('logging', {})
        log_dir = log_config.get('log_dir', './log')
        
        # 创建日志目录
        Path(log_dir).mkdir(parents=True, exist_ok=True)
        
        # 生成日志文件名
        date_str = datetime.now().strftime('%Y%m%d')
        log_file = os.path.join(log_dir, f'eddy_{date_str}.log')
        
        # 轮转文件处理器
        max_bytes = log_config.get('max_file_size', 10) * 1024 * 1024  # MB转字节
        backup_count = log_config.get('backup_count', 5)
        
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger
    
    def update_config_date(self, target_date: datetime) -> bool:
        """
        更新配置文件中的日期
        
        Args:
            target_date: 目标日期
            
        Returns:
            bool: 是否更新成功
        """
        try:
            date_str = target_date.strftime('%Y_%m_%d')
            date_compact = target_date.strftime('%Y%m%d')
            
            # 更新配置数据
            self.config['data']['date_folder'] = date_str
            
            # 更新输入文件路径
            base_dir = self.config['data']['base_dir']
            filename = f"nrt_global_allsat_phy_l4_{date_compact}_{date_compact}.nc"
            self.config['data']['input_file'] = f"{base_dir}/{date_str}/input/{filename}"
            
            # 更新输出路径
            self.config['output']['output_dir'] = f"{base_dir}/{date_str}/output"
            self.config['output']['anticyclonic_nc'] = f"{base_dir}/{date_str}/output/Anticyclonic_{date_compact}.nc"
            self.config['output']['cyclonic_nc'] = f"{base_dir}/{date_str}/output/Cyclonic_{date_compact}.nc"
            self.config['output']['geojson_path'] = f"./output/{date_str}/eddies_{date_compact}.geojson"
            
            # 更新日志文件路径
            log_dir = self.config['logging']['log_dir']
            self.config['logging']['log_file'] = f"{log_dir}/eddy_{date_compact}.log"
            
            # 写回配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True, 
                         sort_keys=False, indent=2)
            
            self.logger.info(f"配置文件日期已更新为: {date_str}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新配置文件失败: {e}")
            return False
    
    def download_data(self, target_date: datetime) -> str:
        """
        下载指定日期的ADT数据
        
        Args:
            target_date: 目标日期
            
        Returns:
            str: 下载的文件路径
        """
        if not self.downloader:
            raise RuntimeError("ADT下载器未初始化，请检查认证信息")
        
        date_str = target_date.strftime('%Y_%m_%d')
        output_dir = os.path.join(
            self.config['data']['base_dir'],
            date_str,
            'input'
        )
        
        self.logger.info(f"开始下载{target_date.strftime('%Y-%m-%d')}的ADT数据...")
        
        file_path = self.downloader.download_adt_data(
            target_date=target_date,
            output_dir=output_dir,
            dataset_id=self.config['data'].get('dataset_id'),
            variables=self.config['data'].get('variables'),
            longitude_range=tuple(self.config['data'].get('longitude_range', [140, 290])),
            latitude_range=tuple(self.config['data'].get('latitude_range', [-40, 20]))
        )
        
        self.logger.info(f"数据下载完成: {file_path}")
        return file_path
    
    def identify_eddies(
        self,
        input_file: str,
        process_date: datetime
    ) -> Tuple[EddiesObservations, EddiesObservations]:
        """
        执行涡旋识别
        
        Args:
            input_file: 输入nc文件路径
            process_date: 处理日期
            
        Returns:
            (反气旋涡对象, 气旋涡对象)
        """
        self.logger.info(f"开始处理文件: {input_file}")
        
        # 获取变量名配置
        var_names = self.config['data']['variable_names']
        lon_name = var_names.get('longitude', 'longitude')
        lat_name = var_names.get('latitude', 'latitude')
        
        # 加载数据
        h = RegularGridDataset(input_file, lon_name, lat_name)
        
        # 获取识别参数
        params = self.config['eddy_identification']
        filter_wavelength = params.get('filter_wavelength', 500)
        filter_order = params.get('filter_order', 3)
        step = params.get('step', 0.002)
        pixel_limit = tuple(params.get('pixel_limit', [5, 2000]))
        shape_error = params.get('shape_error', 55.0)
        
        # 应用高通滤波
        self.logger.info(f"应用贝塞尔高通滤波，截止波长: {filter_wavelength}km")
        h.bessel_high_filter("adt", filter_wavelength, order=filter_order)
        
        # 涡旋识别
        self.logger.info("开始涡旋识别...")
        anticyclonic, cyclonic = h.eddy_identification(
            "adt",
            var_names.get('u_velocity', 'ugos'),
            var_names.get('v_velocity', 'vgos'),
            process_date,
            step,
            pixel_limit=pixel_limit,
            shape_error=shape_error
        )
        
        self.logger.info(f"识别完成 - 反气旋涡: {len(anticyclonic)}个, 气旋涡: {len(cyclonic)}个")
        
        return anticyclonic, cyclonic
    
    def export_to_netcdf(
        self,
        anticyclonic: EddiesObservations,
        cyclonic: EddiesObservations,
        output_dir: str,
        process_date: datetime
    ) -> Tuple[str, str]:
        """导出涡旋数据为nc文件"""
        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        date_str = process_date.strftime("%Y%m%d")
        anticyclonic_file = os.path.join(output_dir, f"Anticyclonic_{date_str}.nc")
        cyclonic_file = os.path.join(output_dir, f"Cyclonic_{date_str}.nc")
        
        # 导出反气旋涡
        self.logger.info(f"导出反气旋涡数据到: {anticyclonic_file}")
        with Dataset(anticyclonic_file, "w") as h:
            anticyclonic.to_netcdf(h)
            
        # 导出气旋涡
        self.logger.info(f"导出气旋涡数据到: {cyclonic_file}")
        with Dataset(cyclonic_file, "w") as h:
            cyclonic.to_netcdf(h)
            
        return anticyclonic_file, cyclonic_file
    
    def _eddy_to_geojson_feature(self, eddy: np.ndarray, eddy_type: str, index: int) -> Dict[str, Any]:
        """将单个涡旋转换为GeoJSON特征"""
        # 提取外轮廓坐标
        lon_coords = eddy['contour_lon_e']
        lat_coords = eddy['contour_lat_e']
        
        # 过滤无效坐标（NaN值）
        valid_mask = ~(np.isnan(lon_coords) | np.isnan(lat_coords))
        lon_coords = lon_coords[valid_mask]
        lat_coords = lat_coords[valid_mask]
        
        # 构建坐标对列表 [lon, lat]
        coordinates = [[float(lon), float(lat)] for lon, lat in zip(lon_coords, lat_coords)]
        
        # 确保轮廓是封闭的
        if len(coordinates) > 0 and coordinates[0] != coordinates[-1]:
            coordinates.append(coordinates[0])
        
        # 构建GeoJSON特征
        feature = {
            "type": "Feature",
            "geometry": {
                "type": "Polygon",
                "coordinates": [coordinates]
            },
            "properties": {
                "eddy_type": eddy_type,
                "eddy_id": f"{eddy_type}_{index}",
                "longitude": float(eddy['lon']) if not np.isnan(eddy['lon']) else None,
                "latitude": float(eddy['lat']) if not np.isnan(eddy['lat']) else None,
                "amplitude": float(eddy['amplitude']) if not np.isnan(eddy['amplitude']) else None,
                "speed_radius": float(eddy['radius_s']) if not np.isnan(eddy['radius_s']) else None,
                "effective_radius": float(eddy['radius_e']) if not np.isnan(eddy['radius_e']) else None,
                "speed_average": float(eddy['speed_average']) if not np.isnan(eddy['speed_average']) else None,
                "time": int(eddy['time']) if not np.isnan(eddy['time']) else None
            }
        }
        
        return feature
    
    def convert_to_geojson(
        self,
        anticyclonic_file: str,
        cyclonic_file: str,
        output_file: str
    ) -> str:
        """将nc文件中的涡旋数据转换为GeoJSON格式"""
        self.logger.info("开始转换为GeoJSON格式...")
        
        features = []
        
        # 加载反气旋涡数据
        try:
            anticyclonic = EddiesObservations.load_file(anticyclonic_file)
            self.logger.info(f"加载反气旋涡数据: {len(anticyclonic)}个")
            
            for i, eddy in enumerate(anticyclonic):
                feature = self._eddy_to_geojson_feature(eddy, "anticyclonic", i)
                features.append(feature)
                
        except Exception as e:
            self.logger.error(f"处理反气旋涡数据时出错: {e}")
        
        # 加载气旋涡数据
        try:
            cyclonic = EddiesObservations.load_file(cyclonic_file)
            self.logger.info(f"加载气旋涡数据: {len(cyclonic)}个")
            
            for i, eddy in enumerate(cyclonic):
                feature = self._eddy_to_geojson_feature(eddy, "cyclonic", i)
                features.append(feature)
                
        except Exception as e:
            self.logger.error(f"处理气旋涡数据时出错: {e}")
        
        # 构建GeoJSON对象
        geojson = {
            "type": "FeatureCollection",
            "features": features,
            "properties": {
                "title": "海洋涡旋数据",
                "description": f"包含{len([f for f in features if f['properties']['eddy_type'] == 'anticyclonic'])}个反气旋涡和{len([f for f in features if f['properties']['eddy_type'] == 'cyclonic'])}个气旋涡",
                "generated_at": datetime.now().isoformat()
            }
        }
        
        # 保存GeoJSON文件
        self.logger.info(f"保存GeoJSON文件到: {output_file}")
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(geojson, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"GeoJSON转换完成，包含{len(features)}个涡旋特征")
        
        return output_file
    
    def run_complete_workflow(self, target_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        运行完整的自动化工作流
        
        Args:
            target_date: 目标日期，默认为今天
            
        Returns:
            处理结果字典
        """
        if target_date is None:
            target_date = datetime.now()
        
        max_retry = self.config.get('automation', {}).get('max_retry', 3)
        
        for attempt in range(max_retry):
            try:
                self.logger.info(f"开始执行涡旋识别工作流 (尝试 {attempt + 1}/{max_retry})")
                self.logger.info(f"目标日期: {target_date.strftime('%Y-%m-%d')}")
                
                # 1. 更新配置文件日期
                self.logger.info("步骤1: 更新配置文件日期")
                if not self.update_config_date(target_date):
                    raise RuntimeError("配置文件日期更新失败")
                
                # 2. 下载数据（如果启用自动下载）
                input_file = self.config['data']['input_file']
                if self.config.get('automation', {}).get('auto_download', True):
                    if not os.path.exists(input_file):
                        self.logger.info("步骤2: 下载ADT数据")
                        input_file = self.download_data(target_date)
                    else:
                        self.logger.info(f"步骤2: 数据文件已存在，跳过下载: {input_file}")
                else:
                    self.logger.info("步骤2: 自动下载已禁用，跳过")
                
                # 3. 涡旋识别
                self.logger.info("步骤3: 涡旋识别")
                anticyclonic, cyclonic = self.identify_eddies(input_file, target_date)
                
                # 4. 导出NC文件
                output_dir = self.config['output']['output_dir']
                if self.config['output'].get('generate_netcdf', True):
                    self.logger.info("步骤4: 导出NetCDF文件")
                    anticyclonic_file, cyclonic_file = self.export_to_netcdf(
                        anticyclonic, cyclonic, output_dir, target_date
                    )
                else:
                    anticyclonic_file = cyclonic_file = None
                
                # 5. 转换为GeoJSON
                geojson_file = None
                if self.config['output'].get('generate_geojson', True):
                    self.logger.info("步骤5: 转换为GeoJSON格式")
                    date_str = target_date.strftime("%Y%m%d")
                    geojson_file = os.path.join(output_dir, f"eddies_{date_str}.geojson")
                    self.convert_to_geojson(anticyclonic_file, cyclonic_file, geojson_file)
                
                # 返回处理结果
                results = {
                    "success": True,
                    "target_date": target_date.strftime('%Y-%m-%d'),
                    "input_file": input_file,
                    "output_dir": output_dir,
                    "anticyclonic_nc": anticyclonic_file,
                    "cyclonic_nc": cyclonic_file,
                    "geojson": geojson_file,
                    "anticyclonic_count": len(anticyclonic),
                    "cyclonic_count": len(cyclonic),
                    "total_eddies": len(anticyclonic) + len(cyclonic),
                    "processing_time": datetime.now().isoformat()
                }
                
                self.logger.info(" 涡旋识别工作流执行完成！")
                self.logger.info(f"总共识别到 {results['total_eddies']} 个涡旋")
                
                return results
                
            except Exception as e:
                self.logger.error(f"工作流执行失败 (尝试 {attempt + 1}/{max_retry}): {e}")
                if attempt == max_retry - 1:
                    # 最后一次尝试失败
                    return {
                        "success": False,
                        "error": str(e),
                        "attempts": max_retry
                    }
                else:
                    self.logger.info(f"将在下次尝试中重试...")


def main():
    """主函数"""
    try:
        # 创建增强版处理器
        processor = EnhancedEddyProcessor()
        
        # 运行完整工作流
        results = processor.run_complete_workflow()
        
        # 输出结果
        if results.get('success'):
            print("\n 处理成功完成！")
            print(f" 处理日期: {results['target_date']}")
            print(f" 反气旋涡: {results['anticyclonic_count']}个")
            print(f" 气旋涡: {results['cyclonic_count']}个")
            print(f" 输出目录: {results['output_dir']}")
            if results['geojson']:
                print(f" GeoJSON文件: {results['geojson']}")
        else:
            print("\n 处理失败！")
            print(f"错误信息: {results.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"程序执行失败: {e}")


if __name__ == "__main__":
    main()