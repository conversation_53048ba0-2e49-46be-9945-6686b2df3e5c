#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADT数据自动下载器
================

基于Copernicus Marine Service自动下载全球海表高度数据
支持指定日期和自动化下载功能

作者：中星海洋
日期：2025-07-10
"""

import os
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Tuple

import copernicusmarine


class ADTDownloader:
    """ADT数据下载器类"""
    
    def __init__(self, username: str, password: str, logger: Optional[logging.Logger] = None):
        """
        初始化下载器
        
        Args:
            username: Copernicus Marine用户名
            password: Copernicus Marine密码
            logger: 日志器，如果不提供将创建新的
        """
        self.username = username
        self.password = password
        self.logger = logger or self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置默认日志器"""
        logger = logging.getLogger(f"{__name__}.ADTDownloader")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def download_adt_data(
        self,
        target_date: datetime,
        output_dir: str,
        dataset_id: str = "cmems_obs-sl_glo_phy-ssh_nrt_allsat-l4-duacs-0.125deg_P1D",
        variables: list = None,
        longitude_range: Tuple[float, float] = (140, 290),
        latitude_range: Tuple[float, float] = (-40, 20),
        use_get_method: bool = False
    ) -> str:
        """
        下载指定日期的ADT数据
        
        Args:
            target_date: 目标日期
            output_dir: 输出目录
            dataset_id: 数据集ID
            variables: 变量列表，默认["adt", "ugos", "vgos"]
            longitude_range: 经度范围 (min, max)
            latitude_range: 纬度范围 (min, max)
            use_get_method: 是否使用get方法而非subset方法
            
        Returns:
            str: 下载的文件路径
        """
        if variables is None:
            variables = ["adt", "ugos", "vgos"]
        
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        date_str = target_date.strftime('%Y%m%d')
        filename = f"nrt_global_allsat_phy_l4_{date_str}_{date_str}.nc"
        output_path = os.path.join(output_dir, filename)
        
        # 如果文件已存在，检查是否需要重新下载
        if os.path.exists(output_path):
            self.logger.info(f"文件已存在: {output_path}")
            return output_path
        
        self.logger.info(f"开始下载ADT数据...")
        self.logger.info(f"  日期: {target_date.strftime('%Y-%m-%d')}")
        self.logger.info(f"  变量: {', '.join(variables)}")
        self.logger.info(f"  经度范围: {longitude_range[0]}° ~ {longitude_range[1]}°")
        self.logger.info(f"  纬度范围: {latitude_range[0]}° ~ {latitude_range[1]}°")
        self.logger.info(f"  输出路径: {output_path}")
        
        try:
            # 格式化日期时间字符串
            datetime_str = target_date.strftime('%Y-%m-%dT00:00:00')
            
            if use_get_method:
                # 使用get方法下载完整数据集
                self.logger.info("使用copernicusmarine.get方法下载...")
                copernicusmarine.get(
                    username=self.username,
                    password=self.password,
                    dataset_id=dataset_id,
                    start_datetime=datetime_str,
                    end_datetime=datetime_str,
                    output_directory=output_dir,
                    output_filename=filename.replace('.nc', ''),
                    disable_progress_bar=False
                )
            else:
                # 使用subset方法下载指定区域数据
                self.logger.info("使用copernicusmarine.subset方法下载...")
                copernicusmarine.subset(
                    username=self.username,
                    password=self.password,
                    dataset_id=dataset_id,
                    variables=variables,
                    minimum_longitude=longitude_range[0],
                    maximum_longitude=longitude_range[1],
                    minimum_latitude=latitude_range[0],
                    maximum_latitude=latitude_range[1],
                    start_datetime=datetime_str,
                    end_datetime=datetime_str,
                    coordinates_selection_method="strict-inside",
                    netcdf_compression_level=1,
                    disable_progress_bar=False,
                    output_directory=output_dir,
                    output_filename=filename.replace('.nc', '')
                )
            
            # 验证文件是否下载成功
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
                self.logger.info(f"下载成功！文件大小: {file_size:.2f} MB")
                return output_path
            else:
                raise FileNotFoundError(f"下载完成但未找到文件: {output_path}")
                
        except Exception as e:
            self.logger.error(f"下载失败: {e}")
            # 清理可能的不完整文件
            if os.path.exists(output_path):
                os.remove(output_path)
            raise
    
    def download_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        output_base_dir: str,
        **kwargs
    ) -> list:
        """
        下载日期范围内的数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            output_base_dir: 输出基础目录
            **kwargs: 其他下载参数
            
        Returns:
            list: 成功下载的文件路径列表
        """
        downloaded_files = []
        current_date = start_date
        
        self.logger.info(f"批量下载数据: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        while current_date <= end_date:
            try:
                # 为每天创建独立的目录
                date_str = current_date.strftime('%Y_%m_%d')
                daily_output_dir = os.path.join(output_base_dir, date_str, 'input')
                
                file_path = self.download_adt_data(
                    target_date=current_date,
                    output_dir=daily_output_dir,
                    **kwargs
                )
                downloaded_files.append(file_path)
                
            except Exception as e:
                self.logger.error(f"下载失败 ({current_date.strftime('%Y-%m-%d')}): {e}")
            
            # 推进到下一天
            current_date += timedelta(days=1)
        
        self.logger.info(f"批量下载完成: {len(downloaded_files)}/{(end_date - start_date).days + 1} 个文件")
        return downloaded_files
    
    def download_today(self, output_base_dir: str, **kwargs) -> str:
        """
        下载今天的数据
        
        Args:
            output_base_dir: 输出基础目录
            **kwargs: 其他下载参数
            
        Returns:
            str: 下载的文件路径
        """
        today = datetime.now()
        date_str = today.strftime('%Y_%m_%d')
        daily_output_dir = os.path.join(output_base_dir, date_str, 'input')
        
        return self.download_adt_data(
            target_date=today,
            output_dir=daily_output_dir,
            **kwargs
        )
    
    def download_yesterday(self, output_base_dir: str, **kwargs) -> str:
        """
        下载昨天的数据（通常用于NRT数据）
        
        Args:
            output_base_dir: 输出基础目录
            **kwargs: 其他下载参数
            
        Returns:
            str: 下载的文件路径
        """
        yesterday = datetime.now() - timedelta(days=1)
        date_str = yesterday.strftime('%Y_%m_%d')
        daily_output_dir = os.path.join(output_base_dir, date_str, 'input')
        
        return self.download_adt_data(
            target_date=yesterday,
            output_dir=daily_output_dir,
            **kwargs
        )


def main():
    """主函数示例"""
    # Copernicus Marine Service 认证信息
    USERNAME = 'hwang36'
    PASSWORD = 'Wang20010113@'
    
    # 创建下载器
    downloader = ADTDownloader(USERNAME, PASSWORD)
    
    # 示例1: 下载今天的数据
    try:
        today_file = downloader.download_today("/data2/eddy")
        print(f"今天数据下载完成: {today_file}")
    except Exception as e:
        print(f"下载今天数据失败: {e}")
    
    # 示例2: 下载指定日期的数据
    try:
        target_date = datetime(2025, 7, 10)
        date_str = target_date.strftime('%Y_%m_%d')
        output_dir = f"/data2/eddy/{date_str}/input"
        
        file_path = downloader.download_adt_data(
            target_date=target_date,
            output_dir=output_dir
        )
        print(f"指定日期数据下载完成: {file_path}")
    except Exception as e:
        print(f"下载指定日期数据失败: {e}")
    
    # 示例3: 批量下载一周的数据
    try:
        start_date = datetime(2025, 7, 1)
        end_date = datetime(2025, 7, 7)
        
        downloaded_files = downloader.download_date_range(
            start_date=start_date,
            end_date=end_date,
            output_base_dir="/data2/eddy"
        )
        print(f"批量下载完成: {len(downloaded_files)} 个文件")
    except Exception as e:
        print(f"批量下载失败: {e}")


if __name__ == "__main__":
    main() 