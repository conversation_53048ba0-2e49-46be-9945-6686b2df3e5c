#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户数据处理模块
===============

根据用户参数裁切全球涡旋识别结果，生成用户特定的geojson文件

作者：中星海洋
日期：2025-07-16
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
from shapely.geometry import Point, box
from shapely.ops import unary_union
import yaml


class UserDataProcessor:
    """用户数据处理器"""
    
    def __init__(self, config_file: str = "config/eddy.yaml"):
        """
        初始化用户数据处理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
        self.logger = self._setup_logger()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            raise RuntimeError(f"无法加载配置文件 {self.config_file}: {e}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('UserDataProcessor')
        logger.setLevel(logging.INFO)
        
        # 避免重复添加handler
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def get_user_parameters(self) -> List[Dict[str, Any]]:
        """
        获取所有用户参数
        
        Returns:
            List[Dict]: 用户参数列表
        """
        user_params = []
        user_params_dir = Path(self.config['user_data']['user_params_dir'])
        
        if not user_params_dir.exists():
            self.logger.warning(f"用户参数目录不存在: {user_params_dir}")
            return user_params
        
        # 遍历所有json文件
        for param_file in user_params_dir.glob("*.json"):
            try:
                with open(param_file, 'r', encoding='utf-8') as f:
                    param_data = json.load(f)
                    param_data['param_file'] = str(param_file)
                    user_params.append(param_data)
            except Exception as e:
                self.logger.error(f"读取用户参数文件失败 {param_file}: {e}")
                continue
        
        self.logger.info(f"找到 {len(user_params)} 个用户参数文件")
        return user_params
    
    def parse_area_bounds(self, area_str: str) -> Tuple[float, float, float, float]:
        """
        解析区域边界字符串
        
        Args:
            area_str: 区域字符串，格式如 "[[147.10047,0],[178.61644,-41]]"
            
        Returns:
            Tuple[float, float, float, float]: (min_lon, min_lat, max_lon, max_lat)
        """
        try:
            # 解析JSON格式的区域字符串
            area_data = json.loads(area_str)
            
            if len(area_data) != 2 or len(area_data[0]) != 2 or len(area_data[1]) != 2:
                raise ValueError("区域数据格式不正确")
            
            lon1, lat1 = area_data[0]
            lon2, lat2 = area_data[1]
            
            # 计算边界
            min_lon = min(lon1, lon2)
            max_lon = max(lon1, lon2)
            min_lat = min(lat1, lat2)
            max_lat = max(lat1, lat2)
            
            return min_lon, min_lat, max_lon, max_lat
            
        except Exception as e:
            self.logger.error(f"解析区域边界失败: {area_str}, 错误: {e}")
            raise
    
    def is_point_in_bounds(self, lon: float, lat: float, bounds: Tuple[float, float, float, float]) -> bool:
        """
        判断点是否在边界内
        
        Args:
            lon: 经度
            lat: 纬度
            bounds: 边界 (min_lon, min_lat, max_lon, max_lat)
            
        Returns:
            bool: 是否在边界内
        """
        min_lon, min_lat, max_lon, max_lat = bounds
        return min_lon <= lon <= max_lon and min_lat <= lat <= max_lat
    
    def is_polygon_in_bounds(self, coordinates: List[List[List[float]]], bounds: Tuple[float, float, float, float]) -> bool:
        """
        判断多边形是否与边界相交
        
        Args:
            coordinates: 多边形坐标
            bounds: 边界 (min_lon, min_lat, max_lon, max_lat)
            
        Returns:
            bool: 是否相交
        """
        min_lon, min_lat, max_lon, max_lat = bounds
        bbox = box(min_lon, min_lat, max_lon, max_lat)
        
        try:
            # 检查多边形的每个环
            for ring in coordinates:
                if len(ring) < 3:
                    continue
                    
                # 检查是否有点在边界内
                for coord in ring:
                    if len(coord) >= 2:
                        lon, lat = coord[0], coord[1]
                        if self.is_point_in_bounds(lon, lat, bounds):
                            return True
                
                # 更精确的几何相交检查（可选）
                # 这里使用简化版本，仅检查是否有点在边界内
                
        except Exception as e:
            self.logger.warning(f"多边形边界检查失败: {e}")
            return False
        
        return False
    
    def filter_geojson_by_bounds(self, geojson_data: Dict[str, Any], bounds: Tuple[float, float, float, float]) -> Dict[str, Any]:
        """
        根据边界过滤geojson数据
        
        Args:
            geojson_data: 原始geojson数据
            bounds: 边界 (min_lon, min_lat, max_lon, max_lat)
            
        Returns:
            Dict: 过滤后的geojson数据
        """
        filtered_features = []
        
        for feature in geojson_data.get('features', []):
            geometry = feature.get('geometry', {})
            geometry_type = geometry.get('type', '')
            coordinates = geometry.get('coordinates', [])
            
            # 处理不同的几何类型
            if geometry_type == 'Point':
                if len(coordinates) >= 2:
                    lon, lat = coordinates[0], coordinates[1]
                    if self.is_point_in_bounds(lon, lat, bounds):
                        filtered_features.append(feature)
                        
            elif geometry_type == 'Polygon':
                if self.is_polygon_in_bounds(coordinates, bounds):
                    filtered_features.append(feature)
                    
            elif geometry_type == 'MultiPolygon':
                # 检查多个多边形中是否有任何一个相交
                for polygon in coordinates:
                    if self.is_polygon_in_bounds([polygon[0]], bounds):
                        filtered_features.append(feature)
                        break
        
        # 构建过滤后的geojson
        filtered_geojson = {
            'type': 'FeatureCollection',
            'features': filtered_features
        }
        
        return filtered_geojson
    
    def create_user_output_dir(self, username: str, date_str: str) -> Path:
        """
        创建用户输出目录
        
        Args:
            username: 用户名
            date_str: 日期字符串
            
        Returns:
            Path: 输出目录路径
        """
        user_output_dir = Path(self.config['user_data']['base_dir']) / username / 'cog' / date_str / 'eddies'
        user_output_dir.mkdir(parents=True, exist_ok=True)
        return user_output_dir
    
    def process_user_data(self, global_geojson_path: str, target_date: datetime) -> Dict[str, Any]:
        """
        处理用户数据，生成用户特定的geojson文件
        
        Args:
            global_geojson_path: 全球geojson文件路径
            target_date: 目标日期
            
        Returns:
            Dict: 处理结果统计
        """
        self.logger.info("="*60)
        self.logger.info("开始处理用户数据")
        self.logger.info("="*60)
        
        # 检查全球geojson文件是否存在
        if not Path(global_geojson_path).exists():
            self.logger.error(f"全球geojson文件不存在: {global_geojson_path}")
            return {'success': False, 'message': '全球geojson文件不存在'}
        
        # 加载全球geojson数据
        try:
            with open(global_geojson_path, 'r', encoding='utf-8') as f:
                global_geojson = json.load(f)
            self.logger.info(f"成功加载全球geojson文件: {len(global_geojson.get('features', []))} 个涡旋")
        except Exception as e:
            self.logger.error(f"加载全球geojson文件失败: {e}")
            return {'success': False, 'message': f'加载全球geojson文件失败: {e}'}
        
        # 获取用户参数
        user_params = self.get_user_parameters()
        
        if not user_params:
            self.logger.warning("没有找到用户参数文件")
            return {'success': True, 'processed_users': 0, 'message': '没有找到用户参数文件'}
        
        # 处理结果统计
        results = {
            'success': True,
            'processed_users': 0,
            'failed_users': 0,
            'user_details': []
        }
        
        date_str = target_date.strftime('%Y_%m_%d')
        
        # 为每个用户处理数据
        for user_param in user_params:
            username = user_param.get('userName', '')
            area_str = user_param.get('area', '')
            param_file = user_param.get('param_file', '')
            
            try:
                self.logger.info(f"处理用户: {username}")
                
                # 检查区域字符串是否有效
                if not area_str or not isinstance(area_str, str):
                    self.logger.warning(f"用户 {username} 的区域参数无效，跳过处理")
                    results['failed_users'] += 1
                    results['user_details'].append({
                        'username': username,
                        'status': 'failed',
                        'error': '区域参数无效'
                    })
                    continue
                
                # 解析用户区域边界
                bounds = self.parse_area_bounds(area_str)
                self.logger.info(f"用户 {username} 的区域边界: {bounds}")
                
                # 过滤geojson数据
                filtered_geojson = self.filter_geojson_by_bounds(global_geojson, bounds)
                filtered_count = len(filtered_geojson.get('features', []))
                
                # 创建用户输出目录
                user_output_dir = self.create_user_output_dir(username, date_str)
                
                # 保存用户特定的geojson文件
                user_geojson_path = user_output_dir / 'eddies.geojson'
                with open(user_geojson_path, 'w', encoding='utf-8') as f:
                    json.dump(filtered_geojson, f, ensure_ascii=False, indent=2)
                
                # 更新用户参数文件的processed状态
                user_param['processed'] = True
                user_param['processed_date'] = target_date.strftime('%Y-%m-%d')
                user_param['filtered_count'] = filtered_count
                
                with open(param_file, 'w', encoding='utf-8') as f:
                    json.dump(user_param, f, ensure_ascii=False, indent=2)
                
                self.logger.info(f"用户 {username} 处理完成: {filtered_count} 个涡旋，保存到 {user_geojson_path}")
                
                results['processed_users'] += 1
                results['user_details'].append({
                    'username': username,
                    'filtered_count': filtered_count,
                    'output_path': str(user_geojson_path),
                    'status': 'success'
                })
                
            except Exception as e:
                self.logger.error(f"处理用户 {username} 失败: {e}")
                results['failed_users'] += 1
                results['user_details'].append({
                    'username': username,
                    'status': 'failed',
                    'error': str(e)
                })
        
        self.logger.info(f"用户数据处理完成: 成功 {results['processed_users']} 个，失败 {results['failed_users']} 个")
        return results 