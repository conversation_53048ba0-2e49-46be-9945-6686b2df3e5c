{"cells": [{"cell_type": "code", "execution_count": 4, "id": "0cd796b7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:51:13Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:51:13Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:51:25Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.46it/s]\n", "INFO - 2025-07-23T03:51:29Z - Successfully downloaded to /data2/eddy/adt/2025_03_01/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-01 -> /data2/eddy/adt/2025_03_01/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:51:38Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:51:38Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:51:49Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:05<00:00,  1.83it/s]\n", "INFO - 2025-07-23T03:51:55Z - Successfully downloaded to /data2/eddy/adt/2025_03_02/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-02 -> /data2/eddy/adt/2025_03_02/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:52:01Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:52:01Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:52:13Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.51it/s]\n", "INFO - 2025-07-23T03:52:17Z - Successfully downloaded to /data2/eddy/adt/2025_03_03/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-03 -> /data2/eddy/adt/2025_03_03/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:52:24Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:52:24Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:52:37Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.47it/s]\n", "INFO - 2025-07-23T03:52:41Z - Successfully downloaded to /data2/eddy/adt/2025_03_04/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-04 -> /data2/eddy/adt/2025_03_04/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:52:48Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:52:48Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:53:01Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.80it/s]\n", "INFO - 2025-07-23T03:53:04Z - Successfully downloaded to /data2/eddy/adt/2025_03_05/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-05 -> /data2/eddy/adt/2025_03_05/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:53:19Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:53:19Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:53:30Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.90it/s]\n", "INFO - 2025-07-23T03:53:34Z - Successfully downloaded to /data2/eddy/adt/2025_03_06/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-06 -> /data2/eddy/adt/2025_03_06/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:53:57Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:53:57Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:54:09Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.79it/s]\n", "INFO - 2025-07-23T03:54:12Z - Successfully downloaded to /data2/eddy/adt/2025_03_07/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-07 -> /data2/eddy/adt/2025_03_07/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:54:20Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:54:20Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:54:31Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.98it/s]\n", "INFO - 2025-07-23T03:54:34Z - Successfully downloaded to /data2/eddy/adt/2025_03_08/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-08 -> /data2/eddy/adt/2025_03_08/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:54:40Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:54:40Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:54:51Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.51it/s]\n", "INFO - 2025-07-23T03:54:55Z - Successfully downloaded to /data2/eddy/adt/2025_03_09/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-09 -> /data2/eddy/adt/2025_03_09/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:55:03Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:55:03Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:55:15Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:02<00:00,  3.50it/s]\n", "INFO - 2025-07-23T03:55:18Z - Successfully downloaded to /data2/eddy/adt/2025_03_10/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-10 -> /data2/eddy/adt/2025_03_10/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:55:28Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:55:28Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:55:39Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.37it/s]\n", "INFO - 2025-07-23T03:55:43Z - Successfully downloaded to /data2/eddy/adt/2025_03_11/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-11 -> /data2/eddy/adt/2025_03_11/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:55:48Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:55:48Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:55:59Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.93it/s]\n", "INFO - 2025-07-23T03:56:02Z - Successfully downloaded to /data2/eddy/adt/2025_03_12/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-12 -> /data2/eddy/adt/2025_03_12/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:56:11Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:56:11Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:56:24Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.35it/s]\n", "INFO - 2025-07-23T03:56:29Z - Successfully downloaded to /data2/eddy/adt/2025_03_13/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-13 -> /data2/eddy/adt/2025_03_13/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:56:35Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:56:35Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:56:48Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.41it/s]\n", "INFO - 2025-07-23T03:56:52Z - Successfully downloaded to /data2/eddy/adt/2025_03_14/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-14 -> /data2/eddy/adt/2025_03_14/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:56:57Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:56:57Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:57:10Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:02<00:00,  3.33it/s]\n", "INFO - 2025-07-23T03:57:13Z - Successfully downloaded to /data2/eddy/adt/2025_03_15/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-15 -> /data2/eddy/adt/2025_03_15/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:57:18Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:57:18Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:57:33Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.88it/s]\n", "INFO - 2025-07-23T03:57:36Z - Successfully downloaded to /data2/eddy/adt/2025_03_16/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-16 -> /data2/eddy/adt/2025_03_16/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:57:41Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:57:41Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:57:53Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.72it/s]\n", "INFO - 2025-07-23T03:57:57Z - Successfully downloaded to /data2/eddy/adt/2025_03_17/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-17 -> /data2/eddy/adt/2025_03_17/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:58:04Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:58:04Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:58:19Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:06<00:00,  1.49it/s]\n", "INFO - 2025-07-23T03:58:26Z - Successfully downloaded to /data2/eddy/adt/2025_03_18/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-18 -> /data2/eddy/adt/2025_03_18/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:58:33Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:58:33Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:58:45Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:05<00:00,  1.81it/s]\n", "INFO - 2025-07-23T03:58:51Z - Successfully downloaded to /data2/eddy/adt/2025_03_19/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-19 -> /data2/eddy/adt/2025_03_19/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:58:59Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:58:59Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:59:11Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.55it/s]\n", "INFO - 2025-07-23T03:59:15Z - Successfully downloaded to /data2/eddy/adt/2025_03_20/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-20 -> /data2/eddy/adt/2025_03_20/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:59:21Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:59:21Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:59:33Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.76it/s]\n", "INFO - 2025-07-23T03:59:37Z - Successfully downloaded to /data2/eddy/adt/2025_03_21/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-21 -> /data2/eddy/adt/2025_03_21/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T03:59:42Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T03:59:42Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T03:59:54Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.73it/s]\n", "INFO - 2025-07-23T03:59:58Z - Successfully downloaded to /data2/eddy/adt/2025_03_22/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-22 -> /data2/eddy/adt/2025_03_22/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:00:13Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:00:13Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:00:24Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.78it/s]\n", "INFO - 2025-07-23T04:00:28Z - Successfully downloaded to /data2/eddy/adt/2025_03_23/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-23 -> /data2/eddy/adt/2025_03_23/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:00:41Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:00:41Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:00:53Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.18it/s]\n", "INFO - 2025-07-23T04:00:58Z - Successfully downloaded to /data2/eddy/adt/2025_03_24/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-24 -> /data2/eddy/adt/2025_03_24/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:01:10Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:01:10Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:01:25Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.69it/s]\n", "INFO - 2025-07-23T04:01:28Z - Successfully downloaded to /data2/eddy/adt/2025_03_25/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-25 -> /data2/eddy/adt/2025_03_25/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:01:49Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:01:49Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:02:01Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  3.06it/s]\n", "INFO - 2025-07-23T04:02:04Z - Successfully downloaded to /data2/eddy/adt/2025_03_26/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-26 -> /data2/eddy/adt/2025_03_26/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:02:09Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:02:09Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:02:20Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:17<00:00,  1.79s/it]\n", "INFO - 2025-07-23T04:02:38Z - Successfully downloaded to /data2/eddy/adt/2025_03_27/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-27 -> /data2/eddy/adt/2025_03_27/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:02:48Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:02:48Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:03:00Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.61it/s]\n", "INFO - 2025-07-23T04:03:04Z - Successfully downloaded to /data2/eddy/adt/2025_03_28/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-28 -> /data2/eddy/adt/2025_03_28/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:03:12Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:03:12Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:03:26Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.35it/s]\n", "INFO - 2025-07-23T04:03:30Z - Successfully downloaded to /data2/eddy/adt/2025_03_29/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-29 -> /data2/eddy/adt/2025_03_29/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:03:53Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:03:53Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:04:05Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.20it/s]\n", "INFO - 2025-07-23T04:04:10Z - Successfully downloaded to /data2/eddy/adt/2025_03_30/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-30 -> /data2/eddy/adt/2025_03_30/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:04:26Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:04:26Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:04:38Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.26it/s]\n", "INFO - 2025-07-23T04:04:42Z - Successfully downloaded to /data2/eddy/adt/2025_03_31/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-03-31 -> /data2/eddy/adt/2025_03_31/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:04:55Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:04:55Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:05:22Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.89it/s]\n", "INFO - 2025-07-23T04:05:26Z - Successfully downloaded to /data2/eddy/adt/2025_04_01/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-01 -> /data2/eddy/adt/2025_04_01/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:05:38Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:05:38Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:05:49Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.60it/s]\n", "INFO - 2025-07-23T04:05:53Z - Successfully downloaded to /data2/eddy/adt/2025_04_02/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-02 -> /data2/eddy/adt/2025_04_02/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:06:00Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:06:00Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:06:11Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.28it/s]\n", "INFO - 2025-07-23T04:06:16Z - Successfully downloaded to /data2/eddy/adt/2025_04_03/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-03 -> /data2/eddy/adt/2025_04_03/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:06:35Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:06:35Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:06:48Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.66it/s]\n", "INFO - 2025-07-23T04:06:52Z - Successfully downloaded to /data2/eddy/adt/2025_04_04/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-04 -> /data2/eddy/adt/2025_04_04/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:06:58Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:06:58Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:07:12Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  3.19it/s]\n", "INFO - 2025-07-23T04:07:15Z - Successfully downloaded to /data2/eddy/adt/2025_04_05/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-05 -> /data2/eddy/adt/2025_04_05/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:08:29Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:08:29Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:08:40Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.45it/s]\n", "INFO - 2025-07-23T04:08:44Z - Successfully downloaded to /data2/eddy/adt/2025_04_06/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-06 -> /data2/eddy/adt/2025_04_06/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:09:02Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:09:02Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:09:14Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:03<00:00,  2.62it/s]\n", "INFO - 2025-07-23T04:09:18Z - Successfully downloaded to /data2/eddy/adt/2025_04_07/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-07 -> /data2/eddy/adt/2025_04_07/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:09:23Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:09:23Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:09:36Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:05<00:00,  1.77it/s]\n", "INFO - 2025-07-23T04:09:41Z - Successfully downloaded to /data2/eddy/adt/2025_04_08/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-08 -> /data2/eddy/adt/2025_04_08/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:09:47Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:09:47Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:09:59Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:05<00:00,  1.91it/s]\n", "INFO - 2025-07-23T04:10:04Z - Successfully downloaded to /data2/eddy/adt/2025_04_09/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-09 -> /data2/eddy/adt/2025_04_09/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:10:17Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:10:17Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:10:29Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.30it/s]\n", "INFO - 2025-07-23T04:10:33Z - Successfully downloaded to /data2/eddy/adt/2025_04_10/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-10 -> /data2/eddy/adt/2025_04_10/adt.nc\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-07-23T04:10:40Z - Selected dataset version: \"202506\"\n", "INFO - 2025-07-23T04:10:40Z - Selected dataset part: \"default\"\n", "INFO - 2025-07-23T04:10:51Z - Starting download. Please wait...\n", "100%|██████████| 10/10 [00:04<00:00,  2.40it/s]\n", "INFO - 2025-07-23T04:10:55Z - Successfully downloaded to /data2/eddy/adt/2025_04_11/adt.nc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["下载完成: 2025-04-11 -> /data2/eddy/adt/2025_04_11/adt.nc\n"]}, {"ename": "CouldNotConnectToAuthenticationSystem", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mTimeoutError\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/connectionpool.py:464\u001b[39m, in \u001b[36mHTTPConnectionPool._make_request\u001b[39m\u001b[34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[39m\n\u001b[32m    463\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m464\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_validate_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    465\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError) \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/connectionpool.py:1093\u001b[39m, in \u001b[36mHTTPSConnectionPool._validate_conn\u001b[39m\u001b[34m(self, conn)\u001b[39m\n\u001b[32m   1092\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m conn.is_closed:\n\u001b[32m-> \u001b[39m\u001b[32m1093\u001b[39m     \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1095\u001b[39m \u001b[38;5;66;03m# TODO revise this, see https://github.com/urllib3/urllib3/issues/2791\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/connection.py:790\u001b[39m, in \u001b[36mHTTPSConnection.connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    788\u001b[39m server_hostname_rm_dot = server_hostname.rstrip(\u001b[33m\"\u001b[39m\u001b[33m.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m790\u001b[39m sock_and_verified = \u001b[43m_ssl_wrap_socket_and_match_hostname\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    791\u001b[39m \u001b[43m    \u001b[49m\u001b[43msock\u001b[49m\u001b[43m=\u001b[49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    792\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcert_reqs\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcert_reqs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    793\u001b[39m \u001b[43m    \u001b[49m\u001b[43mssl_version\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mssl_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    794\u001b[39m \u001b[43m    \u001b[49m\u001b[43mssl_minimum_version\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mssl_minimum_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    795\u001b[39m \u001b[43m    \u001b[49m\u001b[43mssl_maximum_version\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mssl_maximum_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    796\u001b[39m \u001b[43m    \u001b[49m\u001b[43mca_certs\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mca_certs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    797\u001b[39m \u001b[43m    \u001b[49m\u001b[43mca_cert_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mca_cert_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    798\u001b[39m \u001b[43m    \u001b[49m\u001b[43mca_cert_data\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mca_cert_data\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    799\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcert_file\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcert_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    800\u001b[39m \u001b[43m    \u001b[49m\u001b[43mkey_file\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mkey_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    801\u001b[39m \u001b[43m    \u001b[49m\u001b[43mkey_password\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mkey_password\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    802\u001b[39m \u001b[43m    \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[43m=\u001b[49m\u001b[43mserver_hostname_rm_dot\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    803\u001b[39m \u001b[43m    \u001b[49m\u001b[43mssl_context\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mssl_context\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    804\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtls_in_tls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtls_in_tls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    805\u001b[39m \u001b[43m    \u001b[49m\u001b[43massert_hostname\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43massert_hostname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    806\u001b[39m \u001b[43m    \u001b[49m\u001b[43massert_fingerprint\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43massert_fingerprint\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    807\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    808\u001b[39m \u001b[38;5;28mself\u001b[39m.sock = sock_and_verified.socket\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/connection.py:969\u001b[39m, in \u001b[36m_ssl_wrap_socket_and_match_hostname\u001b[39m\u001b[34m(sock, cert_reqs, ssl_version, ssl_minimum_version, ssl_maximum_version, cert_file, key_file, key_password, ca_certs, ca_cert_dir, ca_cert_data, assert_hostname, assert_fingerprint, server_hostname, ssl_context, tls_in_tls)\u001b[39m\n\u001b[32m    967\u001b[39m         server_hostname = normalized\n\u001b[32m--> \u001b[39m\u001b[32m969\u001b[39m ssl_sock = \u001b[43mssl_wrap_socket\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    970\u001b[39m \u001b[43m    \u001b[49m\u001b[43msock\u001b[49m\u001b[43m=\u001b[49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    971\u001b[39m \u001b[43m    \u001b[49m\u001b[43mkeyfile\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkey_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    972\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcertfile\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcert_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    973\u001b[39m \u001b[43m    \u001b[49m\u001b[43mkey_password\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkey_password\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    974\u001b[39m \u001b[43m    \u001b[49m\u001b[43mca_certs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mca_certs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    975\u001b[39m \u001b[43m    \u001b[49m\u001b[43mca_cert_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mca_cert_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    976\u001b[39m \u001b[43m    \u001b[49m\u001b[43mca_cert_data\u001b[49m\u001b[43m=\u001b[49m\u001b[43mca_cert_data\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    977\u001b[39m \u001b[43m    \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[43m=\u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    978\u001b[39m \u001b[43m    \u001b[49m\u001b[43mssl_context\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    979\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtls_in_tls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtls_in_tls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    980\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    982\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/util/ssl_.py:480\u001b[39m, in \u001b[36mssl_wrap_socket\u001b[39m\u001b[34m(sock, keyfile, certfile, cert_reqs, ca_certs, server_hostname, ssl_version, ciphers, ssl_context, ca_cert_dir, key_password, ca_cert_data, tls_in_tls)\u001b[39m\n\u001b[32m    478\u001b[39m context.set_alpn_protocols(ALPN_PROTOCOLS)\n\u001b[32m--> \u001b[39m\u001b[32m480\u001b[39m ssl_sock = \u001b[43m_ssl_wrap_socket_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtls_in_tls\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    481\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m ssl_sock\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/util/ssl_.py:524\u001b[39m, in \u001b[36m_ssl_wrap_socket_impl\u001b[39m\u001b[34m(sock, ssl_context, tls_in_tls, server_hostname)\u001b[39m\n\u001b[32m    522\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m SSLTransport(sock, ssl_context, server_hostname)\n\u001b[32m--> \u001b[39m\u001b[32m524\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mssl_context\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrap_socket\u001b[49m\u001b[43m(\u001b[49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[43m=\u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/ssl.py:455\u001b[39m, in \u001b[36mSSLContext.wrap_socket\u001b[39m\u001b[34m(self, sock, server_side, do_handshake_on_connect, suppress_ragged_eofs, server_hostname, session)\u001b[39m\n\u001b[32m    449\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mwrap_socket\u001b[39m(\u001b[38;5;28mself\u001b[39m, sock, server_side=\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m    450\u001b[39m                 do_handshake_on_connect=\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[32m    451\u001b[39m                 suppress_ragged_eofs=\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[32m    452\u001b[39m                 server_hostname=\u001b[38;5;28;01mNone\u001b[39;00m, session=\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m    453\u001b[39m     \u001b[38;5;66;03m# SSLSocket class handles server_hostname encoding before it calls\u001b[39;00m\n\u001b[32m    454\u001b[39m     \u001b[38;5;66;03m# ctx._wrap_socket()\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m455\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msslsocket_class\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_create\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    456\u001b[39m \u001b[43m        \u001b[49m\u001b[43msock\u001b[49m\u001b[43m=\u001b[49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    457\u001b[39m \u001b[43m        \u001b[49m\u001b[43mserver_side\u001b[49m\u001b[43m=\u001b[49m\u001b[43mserver_side\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    458\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdo_handshake_on_connect\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdo_handshake_on_connect\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    459\u001b[39m \u001b[43m        \u001b[49m\u001b[43msuppress_ragged_eofs\u001b[49m\u001b[43m=\u001b[49m\u001b[43msuppress_ragged_eofs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    460\u001b[39m \u001b[43m        \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[43m=\u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    461\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    462\u001b[39m \u001b[43m        \u001b[49m\u001b[43msession\u001b[49m\u001b[43m=\u001b[49m\u001b[43msession\u001b[49m\n\u001b[32m    463\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/ssl.py:1041\u001b[39m, in \u001b[36mSSLSocket._create\u001b[39m\u001b[34m(cls, sock, server_side, do_handshake_on_connect, suppress_ragged_eofs, server_hostname, context, session)\u001b[39m\n\u001b[32m   1040\u001b[39m                 \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mdo_handshake_on_connect should not be specified for non-blocking sockets\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1041\u001b[39m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mdo_handshake\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1042\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/ssl.py:1319\u001b[39m, in \u001b[36mSSLSocket.do_handshake\u001b[39m\u001b[34m(self, block)\u001b[39m\n\u001b[32m   1318\u001b[39m         \u001b[38;5;28mself\u001b[39m.settimeout(\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m-> \u001b[39m\u001b[32m1319\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_sslobj\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdo_handshake\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1320\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "\u001b[31mTimeoutError\u001b[39m: _ssl.c:993: The handshake operation timed out", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mReadTimeoutError\u001b[39m                          <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/requests/adapters.py:667\u001b[39m, in \u001b[36mHTTPAdapter.send\u001b[39m\u001b[34m(self, request, stream, timeout, verify, cert, proxies)\u001b[39m\n\u001b[32m    666\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m667\u001b[39m     resp = \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    668\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    669\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m=\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    670\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m.\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    671\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m.\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    672\u001b[39m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    673\u001b[39m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    674\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    675\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    676\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    677\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    678\u001b[39m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    679\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    681\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/connectionpool.py:841\u001b[39m, in \u001b[36mHTTPConnectionPool.urlopen\u001b[39m\u001b[34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[39m\n\u001b[32m    839\u001b[39m     new_e = ProtocolError(\u001b[33m\"\u001b[39m\u001b[33mConnection aborted.\u001b[39m\u001b[33m\"\u001b[39m, new_e)\n\u001b[32m--> \u001b[39m\u001b[32m841\u001b[39m retries = \u001b[43mretries\u001b[49m\u001b[43m.\u001b[49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    842\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[43m=\u001b[49m\u001b[43mnew_e\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[43m=\u001b[49m\u001b[43msys\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[32m    843\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    844\u001b[39m retries.sleep()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/util/retry.py:474\u001b[39m, in \u001b[36mRetry.increment\u001b[39m\u001b[34m(self, method, url, response, error, _pool, _stacktrace)\u001b[39m\n\u001b[32m    473\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m read \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mor\u001b[39;00m method \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m._is_method_retryable(method):\n\u001b[32m--> \u001b[39m\u001b[32m474\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[43mreraise\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mtype\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43merror\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    475\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m read \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/util/util.py:39\u001b[39m, in \u001b[36mreraise\u001b[39m\u001b[34m(tp, value, tb)\u001b[39m\n\u001b[32m     38\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m value.with_traceback(tb)\n\u001b[32m---> \u001b[39m\u001b[32m39\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m value\n\u001b[32m     40\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/connectionpool.py:787\u001b[39m, in \u001b[36mHTTPConnectionPool.urlopen\u001b[39m\u001b[34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[39m\n\u001b[32m    786\u001b[39m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m787\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    788\u001b[39m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    789\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    790\u001b[39m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    791\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    792\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    793\u001b[39m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    794\u001b[39m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    795\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    796\u001b[39m \u001b[43m    \u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[43m=\u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    797\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    798\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    799\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    800\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    802\u001b[39m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/connectionpool.py:488\u001b[39m, in \u001b[36mHTTPConnectionPool._make_request\u001b[39m\u001b[34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[39m\n\u001b[32m    487\u001b[39m         new_e = _wrap_proxy_error(new_e, conn.proxy.scheme)\n\u001b[32m--> \u001b[39m\u001b[32m488\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m new_e\n\u001b[32m    490\u001b[39m \u001b[38;5;66;03m# conn.request() calls http.client.*.request, not the method in\u001b[39;00m\n\u001b[32m    491\u001b[39m \u001b[38;5;66;03m# urllib3.request. It also calls makefile (recv) on the socket.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/connectionpool.py:466\u001b[39m, in \u001b[36mHTTPConnectionPool._make_request\u001b[39m\u001b[34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[39m\n\u001b[32m    465\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m466\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_raise_timeout\u001b[49m\u001b[43m(\u001b[49m\u001b[43merr\u001b[49m\u001b[43m=\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m=\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout_value\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    467\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/urllib3/connectionpool.py:367\u001b[39m, in \u001b[36mHTTPConnectionPool._raise_timeout\u001b[39m\u001b[34m(self, err, url, timeout_value)\u001b[39m\n\u001b[32m    366\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(err, SocketTimeout):\n\u001b[32m--> \u001b[39m\u001b[32m367\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m ReadTimeoutError(\n\u001b[32m    368\u001b[39m         \u001b[38;5;28mself\u001b[39m, url, \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mRead timed out. (read timeout=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtimeout_value\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m)\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    369\u001b[39m     ) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n\u001b[32m    371\u001b[39m \u001b[38;5;66;03m# See the above comment about EAGAIN in Python 3.\u001b[39;00m\n", "\u001b[31mReadTimeoutError\u001b[39m: HTTPSConnectionPool(host='auth.marine.copernicus.eu', port=443): Read timed out. (read timeout=60.0)", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mReadTimeout\u001b[39m                               <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/core_functions/credentials_utils.py:481\u001b[39m, in \u001b[36m_validate_and_get_user\u001b[39m\u001b[34m(username, password)\u001b[39m\n\u001b[32m    480\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m481\u001b[39m     result = \u001b[43m_get_user_new_system\u001b[49m\u001b[43m(\u001b[49m\u001b[43musername\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    482\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/core_functions/credentials_utils.py:498\u001b[39m, in \u001b[36m_get_user_new_system\u001b[39m\u001b[34m(username, password)\u001b[39m\n\u001b[32m    497\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m498\u001b[39m     user = \u001b[43m_check_credentials_with_cas\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    499\u001b[39m \u001b[43m        \u001b[49m\u001b[43musername\u001b[49m\u001b[43m=\u001b[49m\u001b[43musername\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpassword\u001b[49m\n\u001b[32m    500\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    501\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m user\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/core_functions/credentials_utils.py:442\u001b[39m, in \u001b[36m_check_credentials_with_cas\u001b[39m\u001b[34m(username, password)\u001b[39m\n\u001b[32m    441\u001b[39m logger.debug(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mPOSTing credentials to \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkeycloak_url\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m442\u001b[39m response = \u001b[43mconn_session\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    443\u001b[39m \u001b[43m    \u001b[49m\u001b[43mkeycloak_url\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproxies\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconn_session\u001b[49m\u001b[43m.\u001b[49m\u001b[43mproxies\u001b[49m\n\u001b[32m    444\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    445\u001b[39m response.raise_for_status()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/requests/sessions.py:637\u001b[39m, in \u001b[36mSession.post\u001b[39m\u001b[34m(self, url, data, json, **kwargs)\u001b[39m\n\u001b[32m    627\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33mr\u001b[39m\u001b[33;03m\"\"\"Sends a POST request. Returns :class:`Response` object.\u001b[39;00m\n\u001b[32m    628\u001b[39m \n\u001b[32m    629\u001b[39m \u001b[33;03m:param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    634\u001b[39m \u001b[33;03m:rtype: requests.Response\u001b[39;00m\n\u001b[32m    635\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m637\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mPOST\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[43m=\u001b[49m\u001b[43mjson\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/core_functions/sessions.py:117\u001b[39m, in \u001b[36mConfiguredRequestsSession.request\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m    116\u001b[39m kwargs.setdefault(\u001b[33m\"\u001b[39m\u001b[33mtimeout\u001b[39m\u001b[33m\"\u001b[39m, HTTPS_TIMEOUT)\n\u001b[32m--> \u001b[39m\u001b[32m117\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/requests/sessions.py:589\u001b[39m, in \u001b[36mSession.request\u001b[39m\u001b[34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[39m\n\u001b[32m    588\u001b[39m send_kwargs.update(settings)\n\u001b[32m--> \u001b[39m\u001b[32m589\u001b[39m resp = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    591\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/requests/sessions.py:703\u001b[39m, in \u001b[36mSession.send\u001b[39m\u001b[34m(self, request, **kwargs)\u001b[39m\n\u001b[32m    702\u001b[39m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m703\u001b[39m r = \u001b[43madapter\u001b[49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    705\u001b[39m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/requests/adapters.py:713\u001b[39m, in \u001b[36mHTTPAdapter.send\u001b[39m\u001b[34m(self, request, stream, timeout, verify, cert, proxies)\u001b[39m\n\u001b[32m    712\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e, ReadTimeoutError):\n\u001b[32m--> \u001b[39m\u001b[32m713\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m ReadTimeout(e, request=request)\n\u001b[32m    714\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e, _InvalidHeader):\n", "\u001b[31mReadTimeout\u001b[39m: HTTPSConnectionPool(host='auth.marine.copernicus.eu', port=443): Read timed out. (read timeout=60.0)", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mCouldNotConnectToAuthenticationSystem\u001b[39m     Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 26\u001b[39m\n\u001b[32m     23\u001b[39m output_filename = \u001b[33mf\u001b[39m\u001b[33m'\u001b[39m\u001b[33madt.nc\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m     25\u001b[39m \u001b[38;5;66;03m# 调用subset函数下载\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m26\u001b[39m \u001b[43mcopernicusmarine\u001b[49m\u001b[43m.\u001b[49m\u001b[43msubset\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     27\u001b[39m \u001b[43m    \u001b[49m\u001b[43musername\u001b[49m\u001b[43m=\u001b[49m\u001b[43musername\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     28\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     29\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdataset_id\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcmems_obs-sl_glo_phy-ssh_nrt_allsat-l4-duacs-0.125deg_P1D\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     30\u001b[39m \u001b[43m    \u001b[49m\u001b[43mvariables\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43madt\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     31\u001b[39m \u001b[43m    \u001b[49m\u001b[43mminimum_longitude\u001b[49m\u001b[43m=\u001b[49m\u001b[43m-\u001b[49m\u001b[32;43m179.9375\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     32\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmaximum_longitude\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m179.9375\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     33\u001b[39m \u001b[43m    \u001b[49m\u001b[43mminimum_latitude\u001b[49m\u001b[43m=\u001b[49m\u001b[43m-\u001b[49m\u001b[32;43m89.9375\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     34\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmaximum_latitude\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m89.9375\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     35\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstart_datetime\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcurrent_date\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstrftime\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43m%\u001b[39;49m\u001b[33;43mY-\u001b[39;49m\u001b[33;43m%\u001b[39;49m\u001b[33;43mm-\u001b[39;49m\u001b[38;5;132;43;01m%d\u001b[39;49;00m\u001b[33;43mT00:00:00\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     36\u001b[39m \u001b[43m    \u001b[49m\u001b[43mend_datetime\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcurrent_date\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstrftime\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43m%\u001b[39;49m\u001b[33;43mY-\u001b[39;49m\u001b[33;43m%\u001b[39;49m\u001b[33;43mm-\u001b[39;49m\u001b[38;5;132;43;01m%d\u001b[39;49;00m\u001b[33;43mT00:00:00\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     37\u001b[39m \u001b[43m    \u001b[49m\u001b[43moutput_directory\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdate_directory\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# 使用日期目录\u001b[39;49;00m\n\u001b[32m     38\u001b[39m \u001b[43m    \u001b[49m\u001b[43moutput_filename\u001b[49m\u001b[43m=\u001b[49m\u001b[43moutput_filename\u001b[49m\n\u001b[32m     39\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m下载完成: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mcurrent_date.strftime(\u001b[33m'\u001b[39m\u001b[33m%\u001b[39m\u001b[33mY-\u001b[39m\u001b[33m%\u001b[39m\u001b[33mm-\u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[33m'\u001b[39m)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m -> \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdate_directory\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moutput_filename\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     43\u001b[39m \u001b[38;5;66;03m# 将当前日期推进一天\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/core_functions/deprecated_options.py:78\u001b[39m, in \u001b[36mdeprecated_python_option.<locals>.deco.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     75\u001b[39m \u001b[38;5;129m@functools\u001b[39m.wraps(f)\n\u001b[32m     76\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mwrapper\u001b[39m(*args, **kwargs):\n\u001b[32m     77\u001b[39m     rename_kwargs(f.\u001b[34m__name__\u001b[39m, kwargs, aliases)\n\u001b[32m---> \u001b[39m\u001b[32m78\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/python_interface/exception_handler.py:17\u001b[39m, in \u001b[36mlog_exception_and_exit.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     15\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mAbort\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     16\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exception:\n\u001b[32m---> \u001b[39m\u001b[32m17\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exception\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/python_interface/exception_handler.py:13\u001b[39m, in \u001b[36mlog_exception_and_exit.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     10\u001b[39m \u001b[38;5;129m@wraps\u001b[39m(function)\n\u001b[32m     11\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mwrapper\u001b[39m(*args, **kwargs):\n\u001b[32m     12\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m13\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunction\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     14\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m click.Abort:\n\u001b[32m     15\u001b[39m         \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mAbort\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/python_interface/subset.py:195\u001b[39m, in \u001b[36msubset\u001b[39m\u001b[34m(dataset_id, dataset_version, dataset_part, username, password, variables, minimum_longitude, maximum_longitude, minimum_latitude, maximum_latitude, minimum_depth, maximum_depth, vertical_axis, start_datetime, end_datetime, minimum_x, maximum_x, minimum_y, maximum_y, coordinates_selection_method, output_filename, file_format, service, request_file, output_directory, credentials_file, motu_api_request, overwrite, skip_existing, dry_run, disable_progress_bar, staging, netcdf_compression_level, netcdf3_compatible, chunk_size_limit, raise_if_updating, platform_ids)\u001b[39m\n\u001b[32m    176\u001b[39m end_datetime = homogenize_datetime(end_datetime)\n\u001b[32m    178\u001b[39m (\n\u001b[32m    179\u001b[39m     minimum_x_axis,\n\u001b[32m    180\u001b[39m     maximum_x_axis,\n\u001b[32m   (...)\u001b[39m\u001b[32m    192\u001b[39m     dataset_part,\n\u001b[32m    193\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m195\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msubset_function\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    196\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdataset_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    197\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdataset_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    198\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdataset_part\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    199\u001b[39m \u001b[43m    \u001b[49m\u001b[43musername\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    200\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    201\u001b[39m \u001b[43m    \u001b[49m\u001b[43mvariables\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    202\u001b[39m \u001b[43m    \u001b[49m\u001b[43mminimum_x_axis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    203\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmaximum_x_axis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    204\u001b[39m \u001b[43m    \u001b[49m\u001b[43mminimum_y_axis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    205\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmaximum_y_axis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    206\u001b[39m \u001b[43m    \u001b[49m\u001b[43mminimum_depth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    207\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmaximum_depth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    208\u001b[39m \u001b[43m    \u001b[49m\u001b[43mvertical_axis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    209\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstart_datetime\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    210\u001b[39m \u001b[43m    \u001b[49m\u001b[43mend_datetime\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    211\u001b[39m \u001b[43m    \u001b[49m\u001b[43mplatform_ids\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    212\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcoordinates_selection_method\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    213\u001b[39m \u001b[43m    \u001b[49m\u001b[43moutput_filename\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    214\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfile_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    215\u001b[39m \u001b[43m    \u001b[49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    216\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequest_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    217\u001b[39m \u001b[43m    \u001b[49m\u001b[43moutput_directory\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    218\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcredentials_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    219\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmotu_api_request\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    220\u001b[39m \u001b[43m    \u001b[49m\u001b[43moverwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    221\u001b[39m \u001b[43m    \u001b[49m\u001b[43mskip_existing\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    222\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdry_run\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    223\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdisable_progress_bar\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    224\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstaging\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    225\u001b[39m \u001b[43m    \u001b[49m\u001b[43mnetcdf_compression_level\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    226\u001b[39m \u001b[43m    \u001b[49m\u001b[43mnetcdf3_compatible\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    227\u001b[39m \u001b[43m    \u001b[49m\u001b[43mchunk_size_limit\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    228\u001b[39m \u001b[43m    \u001b[49m\u001b[43mraise_if_updating\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    229\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/core_functions/subset.py:130\u001b[39m, in \u001b[36msubset_function\u001b[39m\u001b[34m(dataset_id, force_dataset_version, force_dataset_part, username, password, variables, minimum_x, maximum_x, minimum_y, maximum_y, minimum_depth, maximum_depth, vertical_axis, start_datetime, end_datetime, platform_ids, coordinates_selection_method, output_filename, file_format, force_service, request_file, output_directory, credentials_file, motu_api_request, overwrite, skip_existing, dry_run, disable_progress_bar, staging, netcdf_compression_level, netcdf3_compatible, chunk_size_limit, raise_if_updating)\u001b[39m\n\u001b[32m    128\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m subset_request.dataset_id:\n\u001b[32m    129\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mPlease provide a dataset id for a subset request.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m130\u001b[39m username, password = \u001b[43mget_and_check_username_password\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    131\u001b[39m \u001b[43m    \u001b[49m\u001b[43musername\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    132\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    133\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcredentials_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    134\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    135\u001b[39m \u001b[38;5;66;03m# Specific treatment for default values:\u001b[39;00m\n\u001b[32m    136\u001b[39m \u001b[38;5;66;03m# In order to not overload arguments with default values\u001b[39;00m\n\u001b[32m    137\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m overwrite:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/core_functions/credentials_utils.py:543\u001b[39m, in \u001b[36mget_and_check_username_password\u001b[39m\u001b[34m(username, password, credentials_file)\u001b[39m\n\u001b[32m    535\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mget_and_check_username_password\u001b[39m(\n\u001b[32m    536\u001b[39m     username: Optional[\u001b[38;5;28mstr\u001b[39m],\n\u001b[32m    537\u001b[39m     password: Optional[\u001b[38;5;28mstr\u001b[39m],\n\u001b[32m    538\u001b[39m     credentials_file: Optional[pathlib.Path],\n\u001b[32m    539\u001b[39m ) -> \u001b[38;5;28mtuple\u001b[39m[\u001b[38;5;28mstr\u001b[39m, \u001b[38;5;28mstr\u001b[39m]:\n\u001b[32m    540\u001b[39m     username, password = get_username_password(\n\u001b[32m    541\u001b[39m         username=username, password=password, credentials_file=credentials_file\n\u001b[32m    542\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m543\u001b[39m     user = \u001b[43m_validate_and_get_user\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    544\u001b[39m \u001b[43m        \u001b[49m\u001b[43musername\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    545\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    546\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    547\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m user:\n\u001b[32m    548\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidUsernameOrPassword(\n\u001b[32m    549\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mLearn how to recover your credentials at: \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    550\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mhttps://help.marine.copernicus.eu/en/articles/\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    551\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33m4444552-i-forgot-my-username-or-my-password-what-should-i-do\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    552\u001b[39m         )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/core_functions/credentials_utils.py:489\u001b[39m, in \u001b[36m_validate_and_get_user\u001b[39m\u001b[34m(username, password)\u001b[39m\n\u001b[32m    485\u001b[39m logger.debug(\n\u001b[32m    486\u001b[39m     \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCould not connect with new authentication system because of: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    487\u001b[39m )\n\u001b[32m    488\u001b[39m logger.debug(\u001b[33m\"\u001b[39m\u001b[33mTrying with old authentication system...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m489\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_are_copernicus_marine_credentials_valid_old_system\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    490\u001b[39m \u001b[43m    \u001b[49m\u001b[43musername\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpassword\u001b[49m\n\u001b[32m    491\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/eddy/lib/python3.12/site-packages/copernicusmarine/core_functions/credentials_utils.py:476\u001b[39m, in \u001b[36m_are_copernicus_marine_credentials_valid_old_system\u001b[39m\u001b[34m(username, password)\u001b[39m\n\u001b[32m    474\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m requests.exceptions.ConnectionError:\n\u001b[32m    475\u001b[39m         number_of_retry -= \u001b[32m1\u001b[39m\n\u001b[32m--> \u001b[39m\u001b[32m476\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m CouldNotConnectToAuthenticationSystem()\n", "\u001b[31mCouldNotConnectToAuthenticationSystem\u001b[39m: "]}], "source": ["import copernicusmarine\n", "from datetime import datetime, timedelta\n", "import os\n", "\n", "username = 'hwang36'\n", "password = 'Wang20010113@'\n", "\n", "# 定义起始日期和结束日期\n", "start_date = datetime(2025, 3, 1)\n", "end_date = datetime(2025, 7, 22)\n", "\n", "# 循环按天处理\n", "current_date = start_date\n", "while current_date <= end_date:\n", "    # 格式化日期为目录名格式 (YYYY_MM_DD)\n", "    dir_date = current_date.strftime('%Y_%m_%d')\n", "    # 创建日期格式的子目录路径\n", "    date_directory = os.path.join('/data2/eddy/adt/', dir_date)\n", "    \n", "    # 确保目录存在，如果不存在则创建\n", "    os.makedirs(date_directory, exist_ok=True)\n", "    \n", "    output_filename = f'adt.nc'\n", "    \n", "    # 调用subset函数下载\n", "    copernicusmarine.subset(\n", "        username=username,\n", "        password=password,\n", "        dataset_id=\"cmems_obs-sl_glo_phy-ssh_nrt_allsat-l4-duacs-0.125deg_P1D\",\n", "        variables=[\"adt\"],\n", "        minimum_longitude=-179.9375,\n", "        maximum_longitude=179.9375,\n", "        minimum_latitude=-89.9375,\n", "        maximum_latitude=89.9375,\n", "        start_datetime=current_date.strftime('%Y-%m-%dT00:00:00'),\n", "        end_datetime=current_date.strftime('%Y-%m-%dT00:00:00'),\n", "        output_directory=date_directory,  # 使用日期目录\n", "        output_filename=output_filename\n", "    )\n", "    \n", "    print(f\"下载完成: {current_date.strftime('%Y-%m-%d')} -> {date_directory}/{output_filename}\")\n", "    \n", "    # 将当前日期推进一天\n", "    current_date += <PERSON><PERSON><PERSON>(days=1)\n", "\n", "print(\"所有日期数据下载完成！\")"]}], "metadata": {"kernelspec": {"display_name": "eddy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}